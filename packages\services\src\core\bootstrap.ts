/**
 * 服务初始化和引导程序
 * 负责设置默认组件和配置
 */

import { getDefaultApiService } from '../services/api-service'
import { getDefaultRestAdapter } from '../adapters/rest-adapter'
import { getDefaultODataAdapter } from '../adapters/odata-adapter'
import { getGlobalPluginManager } from './plugin-system'
import { getGlobalContainer } from './container'
import {
  createLoggingMiddleware,
  createPerformanceMiddleware,
} from './middleware'
import { createDefaultNormalizer } from './normalizers'

/**
 * 初始化配置
 */
export interface InitializeOptions {
  // 是否启用默认中间件
  enableDefaultMiddlewares?: boolean
  // 是否启用日志中间件
  enableLogging?: boolean
  // 是否启用性能监控中间件
  enablePerformance?: boolean
  // 自定义适配器
  adapters?: Array<{
    name: string
    adapter: any
  }>
  // 自定义中间件
  middlewares?: Array<{
    name: string
    middleware: any
  }>
  // 自定义标准化器
  normalizers?: Array<{
    name: string
    normalizer: any
  }>
}

/**
 * 初始化服务框架
 */
export async function initialize(
  options: InitializeOptions = {}
): Promise<void> {
  const {
    enableDefaultMiddlewares = true,
    enableLogging = true,
    enablePerformance = false,
    adapters = [],
    middlewares = [],
    normalizers = [],
  } = options

  // 1. 初始化服务容器
  const container = getGlobalContainer()

  // 2. 注册默认适配器
  container.register('rest-adapter', () => getDefaultRestAdapter())
  container.register('odata-adapter', () => getDefaultODataAdapter())

  // 3. 注册自定义适配器
  for (const { name, adapter } of adapters) {
    container.register(`${name}-adapter`, () => adapter)
  }

  // 4. 注册默认标准化器
  container.register('config-normalizer', () => createDefaultNormalizer())

  // 5. 注册自定义标准化器
  for (const { name, normalizer } of normalizers) {
    container.register(`${name}-normalizer`, () => normalizer)
  }

  // 6. 获取 API 服务并设置中间件
  const apiService = getDefaultApiService()
  const middlewareManager = apiService.getMiddlewareManager()

  // 7. 注册默认中间件
  if (enableDefaultMiddlewares) {
    if (enableLogging) {
      middlewareManager.use(
        createLoggingMiddleware({
          logRequests: true,
          logResponses: true,
          logErrors: true,
        })
      )
    }

    if (enablePerformance) {
      middlewareManager.use(createPerformanceMiddleware())
    }
  }

  // 8. 注册自定义中间件
  for (const { middleware } of middlewares) {
    middlewareManager.use(middleware)
  }

  // 9. 初始化插件管理器
  getGlobalPluginManager()

  console.log('Services framework initialized successfully')
}

/**
 * 快速初始化（使用默认配置）
 */
export async function quickStart(): Promise<void> {
  await initialize({
    enableDefaultMiddlewares: true,
    enableLogging: true,
    enablePerformance: false,
  })
}

/**
 * 开发模式初始化（启用所有调试功能）
 */
export async function initializeForDevelopment(): Promise<void> {
  await initialize({
    enableDefaultMiddlewares: true,
    enableLogging: true,
    enablePerformance: true,
  })
}

/**
 * 生产模式初始化（最小化日志）
 */
export async function initializeForProduction(): Promise<void> {
  await initialize({
    enableDefaultMiddlewares: false,
    enableLogging: false,
    enablePerformance: false,
  })
}

/**
 * 重置服务框架
 */
export async function reset(): Promise<void> {
  // 清除插件
  const pluginManager = getGlobalPluginManager()
  await pluginManager.clear()

  // 清除容器
  const container = getGlobalContainer()
  container.clear()

  // 清除中间件
  const apiService = getDefaultApiService()
  const middlewareManager = apiService.getMiddlewareManager()
  middlewareManager.clear()

  console.log('Services framework reset successfully')
}

/**
 * 获取框架状态
 */
export function getFrameworkStatus(): {
  container: {
    services: string[]
  }
  plugins: {
    installed: string[]
  }
  middlewares: {
    registered: string[]
  }
} {
  const container = getGlobalContainer()
  const pluginManager = getGlobalPluginManager()
  const apiService = getDefaultApiService()
  const middlewareManager = apiService.getMiddlewareManager()

  return {
    container: {
      services: (container as any).getServiceNames
        ? (container as any).getServiceNames()
        : [],
    },
    plugins: {
      installed: pluginManager.getInstalledPlugins().map((p) => p.name),
    },
    middlewares: {
      registered: middlewareManager.getAll().map((m) => m.name),
    },
  }
}

/**
 * 检查框架是否已初始化
 */
export function isInitialized(): boolean {
  const container = getGlobalContainer()
  return container.has('rest-adapter') && container.has('odata-adapter')
}

/**
 * 自动初始化（如果尚未初始化）
 */
export async function autoInitialize(): Promise<void> {
  if (!isInitialized()) {
    await quickStart()
  }
}

/**
 * 创建自定义初始化配置
 */
export function createInitializeConfig(
  overrides: Partial<InitializeOptions> = {}
): InitializeOptions {
  return {
    enableDefaultMiddlewares: true,
    enableLogging: true,
    enablePerformance: false,
    adapters: [],
    middlewares: [],
    normalizers: [],
    ...overrides,
  }
}

/**
 * 验证初始化配置
 */
export function validateInitializeConfig(config: InitializeOptions): string[] {
  const errors: string[] = []

  // 验证适配器
  if (config.adapters) {
    for (const adapter of config.adapters) {
      if (!adapter.name || typeof adapter.name !== 'string') {
        errors.push('Adapter name must be a non-empty string')
      }
      if (!adapter.adapter) {
        errors.push(`Adapter '${adapter.name}' is missing implementation`)
      }
    }
  }

  // 验证中间件
  if (config.middlewares) {
    for (const middleware of config.middlewares) {
      if (!middleware.name || typeof middleware.name !== 'string') {
        errors.push('Middleware name must be a non-empty string')
      }
      if (!middleware.middleware) {
        errors.push(`Middleware '${middleware.name}' is missing implementation`)
      }
    }
  }

  // 验证标准化器
  if (config.normalizers) {
    for (const normalizer of config.normalizers) {
      if (!normalizer.name || typeof normalizer.name !== 'string') {
        errors.push('Normalizer name must be a non-empty string')
      }
      if (!normalizer.normalizer) {
        errors.push(`Normalizer '${normalizer.name}' is missing implementation`)
      }
    }
  }

  return errors
}
