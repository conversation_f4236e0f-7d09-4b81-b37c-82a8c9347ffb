/**
 * 中间件管理器
 * 提供请求/响应的中间件处理功能
 */

import type { Middleware } from './interfaces'
import type { ApiResponse, RequestContext } from './types'

/**
 * 中间件管理器
 */
export class MiddlewareManager {
  private middlewares: Middleware[] = []

  /**
   * 注册中间件
   */
  use(middleware: Middleware): void {
    // 检查是否已存在同名中间件
    const existingIndex = this.middlewares.findIndex(
      (m) => m.name === middleware.name
    )

    if (existingIndex >= 0) {
      // 替换现有中间件
      this.middlewares[existingIndex] = middleware
    } else {
      // 添加新中间件
      this.middlewares.push(middleware)
    }
  }

  /**
   * 移除中间件
   */
  remove(name: string): boolean {
    const index = this.middlewares.findIndex((m) => m.name === name)
    if (index >= 0) {
      this.middlewares.splice(index, 1)
      return true
    }
    return false
  }

  /**
   * 清除所有中间件
   */
  clear(): void {
    this.middlewares = []
  }

  /**
   * 获取所有中间件
   */
  getAll(): Middleware[] {
    return [...this.middlewares]
  }

  /**
   * 执行请求前中间件
   */
  async executeBeforeRequest<TConfig = any>(
    config: TConfig,
    context: RequestContext
  ): Promise<TConfig> {
    let processedConfig = config

    for (const middleware of this.middlewares) {
      if (middleware.beforeRequest) {
        try {
          processedConfig = await middleware.beforeRequest(
            processedConfig,
            context
          )
        } catch (error) {
          console.warn(
            `Middleware '${middleware.name}' beforeRequest failed:`,
            error
          )
          // 继续执行其他中间件
        }
      }
    }

    return processedConfig
  }

  /**
   * 执行响应后中间件
   */
  async executeAfterResponse<TConfig = any>(
    response: ApiResponse,
    config: TConfig,
    context: RequestContext
  ): Promise<ApiResponse> {
    let processedResponse = response

    // 逆序执行响应中间件
    for (let i = this.middlewares.length - 1; i >= 0; i--) {
      const middleware = this.middlewares[i]
      if (middleware.afterResponse) {
        try {
          processedResponse = await middleware.afterResponse(
            processedResponse,
            config,
            context
          )
        } catch (error) {
          console.warn(
            `Middleware '${middleware.name}' afterResponse failed:`,
            error
          )
          // 继续执行其他中间件
        }
      }
    }

    return processedResponse
  }

  /**
   * 执行错误处理中间件
   */
  async executeOnError<TConfig = any>(
    error: Error,
    config: TConfig,
    context: RequestContext
  ): Promise<ApiResponse | void> {
    for (const middleware of this.middlewares) {
      if (middleware.onError) {
        try {
          const result = await middleware.onError(error, config, context)
          if (result) {
            // 如果中间件返回了响应，则使用该响应
            return result
          }
        } catch (middlewareError) {
          console.warn(
            `Middleware '${middleware.name}' onError failed:`,
            middlewareError
          )
          // 继续执行其他中间件
        }
      }
    }
  }
}

/**
 * 创建日志中间件
 */
export function createLoggingMiddleware(
  options: {
    logRequests?: boolean
    logResponses?: boolean
    logErrors?: boolean
  } = {}
): Middleware {
  const { logRequests = true, logResponses = true, logErrors = true } = options

  return {
    name: 'logging',

    async beforeRequest(config, context) {
      if (logRequests) {
        console.log('[Request]', {
          config,
          context,
          timestamp: new Date().toISOString(),
        })
      }
      return config
    },

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async afterResponse(response, config, context) {
      if (logResponses) {
        console.log('[Response]', {
          success: response.success,
          dataLength: Array.isArray(response.data) ? response.data.length : 1,
          timestamp: new Date().toISOString(),
        })
      }
      return response
    },

    async onError(error, config, context) {
      if (logErrors) {
        console.error('[Error]', {
          error: error.message,
          config,
          context,
          timestamp: new Date().toISOString(),
        })
      }
      // 返回 undefined 表示不处理错误，让其他中间件或默认处理器处理
      return undefined
    },
  }
}

/**
 * 创建性能监控中间件
 */
export function createPerformanceMiddleware(): Middleware {
  const timers = new Map<string, number>()

  return {
    name: 'performance',

    async beforeRequest(config, context) {
      const requestId = `${Date.now()}-${Math.random()}`
      context.__requestId = requestId
      timers.set(requestId, performance.now())
      return config
    },

    async afterResponse(response, config, context) {
      const requestId = context.__requestId
      if (requestId && timers.has(requestId)) {
        const startTime = timers.get(requestId)!
        const duration = performance.now() - startTime
        timers.delete(requestId)

        console.log(
          `[Performance] Request completed in ${duration.toFixed(2)}ms`
        )

        // 添加性能信息到响应中
        return {
          ...response,
          __performance: {
            duration,
            timestamp: new Date().toISOString(),
          },
        }
      }
      return response
    },

    async onError(error, config, context) {
      const requestId = context.__requestId
      if (requestId && timers.has(requestId)) {
        const startTime = timers.get(requestId)!
        const duration = performance.now() - startTime
        timers.delete(requestId)

        console.log(
          `[Performance] Request failed after ${duration.toFixed(2)}ms`
        )
      }
      return undefined
    },
  }
}

/**
 * 创建重试中间件
 */
export function createRetryMiddleware(
  options: {
    maxRetries?: number
    retryDelay?: number
    retryCondition?: (error: Error) => boolean
  } = {}
): Middleware {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    retryCondition = (error) =>
      error.message.includes('network') || error.message.includes('timeout'),
  } = options

  return {
    name: 'retry',

    async onError(error, _config, context) {
      const retryCount = (context.__retryCount || 0) + 1

      if (retryCount <= maxRetries && retryCondition(error)) {
        console.log(
          `[Retry] Attempt ${retryCount}/${maxRetries} after ${retryDelay}ms`
        )

        // 等待指定时间后重试
        await new Promise((resolve) => setTimeout(resolve, retryDelay))

        // 更新重试计数
        context.__retryCount = retryCount

        // 这里需要重新执行请求，但由于中间件的限制，
        // 实际的重试逻辑应该在更高层实现
        throw new Error('RETRY_NEEDED')
      }
      return undefined
    },
  }
}
