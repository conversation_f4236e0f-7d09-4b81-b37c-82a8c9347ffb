/**
 * 统一的 API 服务
 * 重构后的版本，遵循单一职责原则和依赖倒置原则
 */

import { ServiceError } from '../core/error-handler'
import { MiddlewareManager } from '../core/middleware'
import { getGlobalContainer } from '../core/container'
import { autoInitialize } from '../core/bootstrap'
import type {
  ConfigNormalizer,
  ProtocolAdapter,
  RequestExecutor,
} from '../core/interfaces'
import type { ApiConfig, ApiResponse, RequestContext } from '../core/types'

/**
 * 重构后的 API 服务
 * 职责：协调适配器、中间件和配置标准化器
 */
export class ApiService implements RequestExecutor<ApiConfig> {
  private adapters = new Map<string, ProtocolAdapter>()
  private normalizers: ConfigNormalizer[] = []
  private middlewareManager = new MiddlewareManager()

  constructor() {
    // 从容器中获取默认适配器和标准化器
    this.initializeDefaults()
  }

  /**
   * 初始化默认组件
   */
  private initializeDefaults(): void {
    const container = getGlobalContainer()
    console.log(container)

    // 尝试获取默认适配器
    try {
      const restAdapter = container.get<ProtocolAdapter>('rest-adapter')
      this.registerAdapter(restAdapter)
    } catch {
      // 如果容器中没有，则延迟加载
    }

    try {
      const odataAdapter = container.get<ProtocolAdapter>('odata-adapter')
      this.registerAdapter(odataAdapter)
    } catch {
      // 如果容器中没有，则延迟加载
    }
  }

  /**
   * 注册协议适配器
   */
  registerAdapter(adapter: ProtocolAdapter): void {
    this.adapters.set(adapter.protocol, adapter)
  }

  /**
   * 移除适配器
   */
  removeAdapter(protocol: string): boolean {
    return this.adapters.delete(protocol)
  }

  /**
   * 注册配置标准化器
   */
  registerNormalizer(normalizer: ConfigNormalizer): void {
    console.log(123465, normalizer)
    this.normalizers.push(normalizer)
  }

  /**
   * 获取中间件管理器
   */
  getMiddlewareManager(): MiddlewareManager {
    return this.middlewareManager
  }

  /**
   * 执行 API 请求
   * 新的实现支持中间件和配置标准化
   */
  async execute(
    config: ApiConfig,
    context: RequestContext = {}
  ): Promise<ApiResponse> {
    try {
      // 1. 执行请求前中间件
      const processedConfig = await this.middlewareManager.executeBeforeRequest(
        config,
        context
      )

      console.log(123, processedConfig)
      // 2. 标准化配置
      const normalizedConfig = this.normalizeConfig(processedConfig)
      // 3. 查找合适的适配器
      const adapter = this.findAdapter(normalizedConfig)
      console.log('adapter', adapter, normalizedConfig)
      if (!adapter) {
        throw new ServiceError(
          `No adapter found for protocol: ${
            normalizedConfig.protocol || 'rest'
          }`,
          'NO_ADAPTER_FOUND'
        )
      }
      // 4. 执行请求
      const response = await adapter.execute(normalizedConfig, context)

      // 5. 执行响应后中间件
      return await this.middlewareManager.executeAfterResponse(
        response,
        normalizedConfig,
        context
      )
    } catch (error) {
      // 6. 执行错误处理中间件
      const middlewareResponse = await this.middlewareManager.executeOnError(
        error as Error,
        config,
        context
      )

      if (middlewareResponse) {
        return middlewareResponse
      }

      // 如果中间件没有处理错误，则抛出原始错误
      throw error
    }
  }

  /**
   * 查找合适的适配器
   */
  private findAdapter(config: ApiConfig): ProtocolAdapter | null {
    const protocol = config.protocol || 'rest'
    const adapter = this.adapters.get(protocol)
    if (adapter && adapter.supports(config)) {
      return adapter
    }

    // 如果没有找到精确匹配，尝试查找支持该配置的适配器
    for (const [, adapter] of this.adapters) {
      if (adapter.supports(config)) {
        return adapter
      }
    }

    return null
  }

  /**
   * 获取已注册的适配器列表
   */
  getAdapters(): ProtocolAdapter[] {
    return Array.from(this.adapters.values())
  }

  /**
   * 标准化配置
   */
  private normalizeConfig(config: ApiConfig): ApiConfig {
    // 使用注册的标准化器处理配置
    for (const normalizer of this.normalizers) {
      if (normalizer.canHandle(config)) {
        return normalizer.normalize(config)
      }
    }

    // 如果没有合适的标准化器，返回原配置
    return config
  }
}

// 默认 API 服务实例
let defaultApiService: ApiService | null = null

/**
 * 获取默认 API 服务实例
 */
export function getDefaultApiService(): ApiService {
  if (!defaultApiService) {
    defaultApiService = new ApiService()
  }
  console.log(123465, defaultApiService)
  return defaultApiService
}

/**
 * 设置默认 API 服务实例
 */
export function setDefaultApiService(service: ApiService): void {
  defaultApiService = service
}

/**
 * 创建 API 服务实例
 */
export function createApiService(): ApiService {
  return new ApiService()
}

/**
 * 执行 API 请求的便捷函数
 */
export async function executeApi(
  config: ApiConfig,
  context: RequestContext = {}
): Promise<ApiResponse> {
  console.log(config, context)

  // 确保框架已初始化
  await autoInitialize()

  return getDefaultApiService().execute(config, context)
}

/**
 * 批量执行 API 请求
 */
export async function executeBatchApi(
  configs: ApiConfig[],
  context: RequestContext = {}
): Promise<ApiResponse[]> {
  const service = getDefaultApiService()
  const promises = configs.map((config) => service.execute(config, context))
  return Promise.all(promises)
}

/**
 * 并发执行 API 请求（有限制）
 */
export async function executeConcurrentApi(
  configs: ApiConfig[],
  context: RequestContext = {},
  concurrency: number = 5
): Promise<ApiResponse[]> {
  const service = getDefaultApiService()
  const results: ApiResponse[] = []

  for (let i = 0; i < configs.length; i += concurrency) {
    const batch = configs.slice(i, i + concurrency)
    const batchPromises = batch.map((config) =>
      service.execute(config, context)
    )
    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)
  }

  return results
}
