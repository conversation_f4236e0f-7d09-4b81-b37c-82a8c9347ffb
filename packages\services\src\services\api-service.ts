/**
 * 统一的 API 服务
 * 提供统一的 API 调用接口，支持多种协议和适配器
 */

import { getDefaultRestAdapter } from '../adapters/rest-adapter'
import { getDefaultODataAdapter } from '../adapters/odata-adapter'
import { ServiceError } from '../core/error-handler'
import {
  RequestCacheManager,
  getGlobalCacheManager,
} from '../core/request-cache'
import {
  CacheStrategyManager,
  getGlobalStrategyManager,
} from '../core/cache-strategy'
import type {
  ApiAdapter,
  ApiConfig,
  ApiResponse,
  LegacyApiSchema,
  ODataApiConfig,
  RequestContext,
  RestApiConfig,
} from '../core/types'

export class ApiService {
  private adapters: ApiAdapter[] = []
  private cacheManager: RequestCacheManager
  private strategyManager: CacheStrategyManager

  constructor(
    cacheManager?: RequestCacheManager,
    strategyManager?: CacheStrategyManager
  ) {
    // 注册默认适配器
    this.registerAdapter(getDefaultRestAdapter())
    this.registerAdapter(getDefaultODataAdapter())

    // 初始化缓存和策略管理器
    this.cacheManager = cacheManager || getGlobalCacheManager()
    this.strategyManager = strategyManager || getGlobalStrategyManager()
  }

  /**
   * 注册适配器
   */
  registerAdapter(adapter: ApiAdapter): void {
    this.adapters.push(adapter)
  }

  /**
   * 移除适配器
   */
  removeAdapter(adapter: ApiAdapter): void {
    const index = this.adapters.indexOf(adapter)
    if (index > -1) {
      this.adapters.splice(index, 1)
    }
  }

  /**
   * 清除所有适配器
   */
  clearAdapters(): void {
    this.adapters = []
  }

  /**
   * 执行 API 请求
   */
  async execute(
    config: ApiConfig,
    context: RequestContext = {}
  ): Promise<ApiResponse> {
    console.log(config)
    // 标准化配置
    const normalizedConfig = this.normalizeConfig(config)

    // 查找合适的适配器
    const adapter = this.findAdapter(normalizedConfig)
    console.log(adapter, normalizedConfig, config)
    if (!adapter) {
      throw new ServiceError(
        `No adapter found for protocol: ${normalizedConfig.protocol || 'rest'}`,
        'NO_ADAPTER_FOUND'
      )
    }

    // 检查是否启用缓存
    const cacheOptions = normalizedConfig.cache
    if (cacheOptions?.enabled === false) {
      // 缓存被禁用，直接执行请求
      return adapter.execute(normalizedConfig, context)
    }

    // 使用缓存策略执行请求
    return this.strategyManager.executeStrategy(
      normalizedConfig,
      context,
      () => adapter.execute(normalizedConfig, context),
      this.cacheManager
    )
  }

  /**
   * 标准化配置（向后兼容）
   */
  private normalizeConfig(config: ApiConfig): RestApiConfig | ODataApiConfig {
    // 如果是新的配置格式，直接返回
    if ('query' in config && config.protocol === 'odata') {
      return config as ODataApiConfig
    }

    // 处理旧的配置格式
    const legacyConfig = config as LegacyApiSchema

    if (legacyConfig.protocol === 'odata') {
      // 转换为新的 OData 配置格式
      return {
        url: legacyConfig.url,
        method: legacyConfig.method,
        protocol: 'odata',
        headers: legacyConfig.headers,
        params: legacyConfig.params,
        body: legacyConfig.body,
        dataPath: legacyConfig.dataPath,
        query: legacyConfig.odata || {},
        timeout: legacyConfig.timeout,
      } as ODataApiConfig
    } else {
      // 转换为新的 REST 配置格式
      return {
        url: legacyConfig.url,
        method: legacyConfig.method,
        protocol: 'rest',
        headers: legacyConfig.headers,
        params: legacyConfig.params,
        body: legacyConfig.body,
        dataPath: legacyConfig.dataPath,
        timeout: legacyConfig.timeout,
      } as RestApiConfig
    }
  }

  /**
   * 查找合适的适配器
   */
  private findAdapter(
    config: RestApiConfig | ODataApiConfig
  ): ApiAdapter | null {
    return this.adapters.find((adapter) => adapter.canHandle(config)) || null
  }

  /**
   * 获取已注册的适配器列表
   */
  getAdapters(): ApiAdapter[] {
    return [...this.adapters]
  }

  /**
   * 获取缓存管理器
   */
  getCacheManager(): RequestCacheManager {
    return this.cacheManager
  }

  /**
   * 获取策略管理器
   */
  getStrategyManager(): CacheStrategyManager {
    return this.strategyManager
  }

  /**
   * 清除所有缓存
   */
  clearCache(): void {
    this.cacheManager.clear()
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return this.cacheManager.getStats()
  }

  /**
   * 设置默认缓存策略
   */
  setDefaultCacheStrategy(strategy: string): void {
    this.strategyManager.setDefaultStrategy(strategy as any)
  }

  /**
   * 为特定 URL 模式设置缓存策略
   */
  setCacheStrategyForPattern(pattern: string, config: any): void {
    this.strategyManager.setStrategyForPattern(pattern, config)
  }
}

// 默认 API 服务实例
let defaultApiService: ApiService | null = null

/**
 * 获取默认 API 服务实例
 */
export function getDefaultApiService(): ApiService {
  if (!defaultApiService) {
    defaultApiService = new ApiService()
  }
  return defaultApiService
}

/**
 * 设置默认 API 服务实例
 */
export function setDefaultApiService(service: ApiService): void {
  defaultApiService = service
}

/**
 * 创建 API 服务实例
 */
export function createApiService(): ApiService {
  return new ApiService()
}

/**
 * 执行 API 请求的便捷函数
 */
export async function executeApi(
  config: ApiConfig,
  context: RequestContext = {}
): Promise<ApiResponse> {
  return getDefaultApiService().execute(config, context)
}

/**
 * 批量执行 API 请求
 */
export async function executeBatchApi(
  configs: ApiConfig[],
  context: RequestContext = {}
): Promise<ApiResponse[]> {
  const service = getDefaultApiService()
  const promises = configs.map((config) => service.execute(config, context))
  return Promise.all(promises)
}

/**
 * 并发执行 API 请求（有限制）
 */
export async function executeConcurrentApi(
  configs: ApiConfig[],
  context: RequestContext = {},
  concurrency: number = 5
): Promise<ApiResponse[]> {
  const service = getDefaultApiService()
  const results: ApiResponse[] = []

  for (let i = 0; i < configs.length; i += concurrency) {
    const batch = configs.slice(i, i + concurrency)
    const batchPromises = batch.map((config) =>
      service.execute(config, context)
    )
    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)
  }

  return results
}
