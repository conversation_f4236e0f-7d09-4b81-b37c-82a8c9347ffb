/**
 * 请求缓存和去重模块
 * 提供请求去重、缓存策略、TTL 管理等功能
 */

import type { ApiConfig, RequestContext } from './types'

// 缓存项接口
export interface CacheItem<T = any> {
  data: T
  timestamp: number
  ttl: number
  key: string
}

// 缓存配置
export interface CacheConfig {
  ttl?: number // 缓存时间（毫秒），默认 5 分钟
  maxSize?: number // 最大缓存数量，默认 100
  enabled?: boolean // 是否启用缓存，默认 true
}

// 请求去重配置
export interface DedupeConfig {
  enabled?: boolean // 是否启用去重，默认 true
  timeout?: number // 去重超时时间（毫秒），默认 30 秒
}

// 缓存策略枚举
export enum CacheStrategy {
  CACHE_FIRST = 'cache-first', // 优先使用缓存
  NETWORK_FIRST = 'network-first', // 优先使用网络
  CACHE_ONLY = 'cache-only', // 仅使用缓存
  NETWORK_ONLY = 'network-only', // 仅使用网络
  STALE_WHILE_REVALIDATE = 'stale-while-revalidate', // 返回缓存，后台更新
}

// 请求缓存管理器
export class RequestCacheManager {
  private cache = new Map<string, CacheItem>()
  private pendingRequests = new Map<string, Promise<any>>()
  private config: Required<CacheConfig & DedupeConfig>

  constructor(config: CacheConfig & DedupeConfig = {}) {
    this.config = {
      ttl: config.ttl || 5 * 60 * 1000, // 5 分钟
      maxSize: config.maxSize || 100,
      enabled: config.enabled !== false,
      timeout: config.timeout || 30 * 1000, // 30 秒
    }

    // 定期清理过期缓存
    this.startCleanupTimer()
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(config: ApiConfig, context: RequestContext = {}): string {
    const keyData = {
      url: config.url,
      method: config.method,
      protocol: config.protocol,
      params: config.params,
      body: config.body,
      context: Object.keys(context).length > 0 ? context : undefined,
    }

    return btoa(JSON.stringify(keyData)).replace(/[+/=]/g, (match) => {
      return { '+': '-', '/': '_', '=': '' }[match] || match
    })
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(item: CacheItem): boolean {
    return Date.now() - item.timestamp < item.ttl
  }

  /**
   * 获取缓存数据
   */
  get<T = any>(key: string): T | null {
    if (!this.config.enabled) return null

    const item = this.cache.get(key)
    if (!item) return null

    if (this.isCacheValid(item)) {
      return item.data
    }

    // 缓存过期，删除
    this.cache.delete(key)
    return null
  }

  /**
   * 设置缓存数据
   */
  set<T = any>(key: string, data: T, ttl?: number): void {
    if (!this.config.enabled) return

    // 检查缓存大小限制
    if (this.cache.size >= this.config.maxSize) {
      this.evictOldest()
    }

    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.config.ttl,
      key,
    }

    this.cache.set(key, item)
  }

  /**
   * 删除缓存
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear()
    this.pendingRequests.clear()
  }

  /**
   * 请求去重处理
   */
  async dedupe<T = any>(key: string, requestFn: () => Promise<T>): Promise<T> {
    if (!this.config.enabled) {
      return requestFn()
    }

    // 检查是否有正在进行的相同请求
    const pendingRequest = this.pendingRequests.get(key)
    if (pendingRequest) {
      return pendingRequest
    }

    // 创建新的请求 Promise
    const requestPromise = this.executeWithTimeout(requestFn, key)
    this.pendingRequests.set(key, requestPromise)

    try {
      const result = await requestPromise
      return result
    } finally {
      // 请求完成后清理
      this.pendingRequests.delete(key)
    }
  }

  /**
   * 带超时的请求执行
   */
  private async executeWithTimeout<T>(
    requestFn: () => Promise<T>,
    key: string
  ): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        this.pendingRequests.delete(key)
        reject(new Error(`Request timeout after ${this.config.timeout}ms`))
      }, this.config.timeout)
    })

    return Promise.race([requestFn(), timeoutPromise])
  }

  /**
   * 淘汰最旧的缓存项
   */
  private evictOldest(): void {
    let oldestKey = ''
    let oldestTime = Date.now()

    for (const [key, item] of this.cache.entries()) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp >= item.ttl) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach((key) => this.cache.delete(key))
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    // 每分钟清理一次过期缓存
    setInterval(() => {
      this.cleanup()
    }, 60 * 1000)
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    let validCount = 0
    let expiredCount = 0

    for (const item of this.cache.values()) {
      if (this.isCacheValid(item)) {
        validCount++
      } else {
        expiredCount++
      }
    }

    return {
      total: this.cache.size,
      valid: validCount,
      expired: expiredCount,
      pending: this.pendingRequests.size,
      maxSize: this.config.maxSize,
      hitRate: this.calculateHitRate(),
    }
  }

  /**
   * 计算缓存命中率（简化实现）
   */
  private calculateHitRate(): number {
    // 这里可以实现更复杂的命中率计算
    // 目前返回一个基于有效缓存比例的估算值
    const stats = this.getStats()
    return stats.total > 0 ? stats.valid / stats.total : 0
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<CacheConfig & DedupeConfig>): void {
    this.config = { ...this.config, ...config }
  }
}

// 全局缓存管理器实例
let globalCacheManager: RequestCacheManager | null = null

/**
 * 获取全局缓存管理器
 */
export function getGlobalCacheManager(): RequestCacheManager {
  if (!globalCacheManager) {
    globalCacheManager = new RequestCacheManager()
  }
  return globalCacheManager
}

/**
 * 设置全局缓存管理器
 */
export function setGlobalCacheManager(manager: RequestCacheManager): void {
  globalCacheManager = manager
}

/**
 * 创建缓存管理器实例
 */
export function createCacheManager(
  config?: CacheConfig & DedupeConfig
): RequestCacheManager {
  return new RequestCacheManager(config)
}
