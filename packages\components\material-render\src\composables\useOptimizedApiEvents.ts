/**
 * 性能优化的 API 事件管理
 * 包含防抖、节流、批量请求等优化功能
 */

import { type Ref, computed, onUnmounted, ref } from 'vue'
import { debounce, throttle } from 'lodash-unified'
import { type ApiExecutionContext, useApiEvents } from './useApiEvents'
import type { ApiConfig } from '@neue-plus/services'

// 批量请求配置
export interface BatchRequestConfig {
  maxBatchSize: number
  batchTimeout: number
  enabled: boolean
}

// 优化选项
export interface OptimizationOptions {
  // 防抖配置
  debounce?: {
    enabled: boolean
    delay: number
    apiNames?: string[] // 指定需要防抖的 API
  }

  // 节流配置
  throttle?: {
    enabled: boolean
    delay: number
    apiNames?: string[] // 指定需要节流的 API
  }

  // 批量请求配置
  batch?: BatchRequestConfig

  // 预加载配置
  preload?: {
    enabled: boolean
    apiNames: string[]
    delay?: number
  }
}

/**
 * 使用优化的 API 事件管理
 */
export function useOptimizedApiEvents(
  apiConfigs: Ref<Record<string, ApiConfig>>,
  optimizationOptions: OptimizationOptions = {}
) {
  // 基础 API 事件管理
  const baseApiEvents = useApiEvents(apiConfigs, {
    enableCache: true,
    enableRetry: true,
    maxRetries: 2,
  })

  // 批量请求队列
  const batchQueue = ref<
    Array<{
      apiName: string
      context: ApiExecutionContext
      resolve: (value: any) => void
      reject: (error: any) => void
    }>
  >([])

  // 防抖函数缓存
  const debouncedFunctions = new Map<string, Function>()

  // 节流函数缓存
  const throttledFunctions = new Map<string, Function>()

  /**
   * 创建防抖版本的 API 函数
   */
  const createDebouncedApi = (
    apiName: string,
    originalFn: Function,
    delay: number
  ) => {
    if (!debouncedFunctions.has(apiName)) {
      const debouncedFn = debounce(originalFn, delay, {
        leading: false,
        trailing: true,
      })
      debouncedFunctions.set(apiName, debouncedFn)
    }
    return debouncedFunctions.get(apiName)!
  }

  /**
   * 创建节流版本的 API 函数
   */
  const createThrottledApi = (
    apiName: string,
    originalFn: Function,
    delay: number
  ) => {
    if (!throttledFunctions.has(apiName)) {
      const throttledFn = throttle(originalFn, delay, {
        leading: true,
        trailing: false,
      })
      throttledFunctions.set(apiName, throttledFn)
    }
    return throttledFunctions.get(apiName)!
  }

  /**
   * 处理批量请求
   */
  const processBatchQueue = async () => {
    if (batchQueue.value.length === 0) return

    const batch = batchQueue.value.splice(
      0,
      optimizationOptions.batch?.maxBatchSize || 10
    )

    try {
      // 并行执行批量请求
      const promises = batch.map(
        async ({ apiName, context, resolve, reject }) => {
          try {
            const result = await baseApiEvents.dynamicApiEvents.value[
              apiName
            ]?.(context)
            resolve(result)
          } catch (error) {
            reject(error)
          }
        }
      )

      await Promise.allSettled(promises)
    } catch (error) {
      console.error('Batch processing failed:', error)
    }
  }

  // 批量处理定时器
  let batchTimer: NodeJS.Timeout | null = null

  /**
   * 添加到批量队列
   */
  const addToBatch = (
    apiName: string,
    context: ApiExecutionContext
  ): Promise<any> => {
    return new Promise((resolve, reject) => {
      batchQueue.value.push({ apiName, context, resolve, reject })

      // 设置批量处理定时器
      if (batchTimer) {
        clearTimeout(batchTimer)
      }

      batchTimer = setTimeout(() => {
        processBatchQueue()
        batchTimer = null
      }, optimizationOptions.batch?.batchTimeout || 100)

      // 如果队列达到最大大小，立即处理
      if (
        batchQueue.value.length >=
        (optimizationOptions.batch?.maxBatchSize || 10)
      ) {
        if (batchTimer) {
          clearTimeout(batchTimer)
          batchTimer = null
        }
        processBatchQueue()
      }
    })
  }

  /**
   * 预加载指定的 API
   */
  const preloadApis = async () => {
    const { preload } = optimizationOptions
    if (!preload?.enabled || !preload.apiNames.length) return

    const delay = preload.delay || 1000

    setTimeout(async () => {
      const preloadPromises = preload.apiNames.map(async (apiName) => {
        try {
          await baseApiEvents.dynamicApiEvents.value[apiName]?.({})
          console.log(`✅ Preloaded API: ${apiName}`)
        } catch (error) {
          console.warn(`⚠️ Failed to preload API: ${apiName}`, error)
        }
      })

      await Promise.allSettled(preloadPromises)
    }, delay)
  }

  /**
   * 优化后的动态 API 事件
   */
  const optimizedApiEvents = computed(() => {
    const result: Record<string, Function> = {}
    const originalEvents = baseApiEvents.dynamicApiEvents.value

    Object.keys(originalEvents).forEach((apiName) => {
      const originalFn = originalEvents[apiName]
      let optimizedFn = originalFn

      // 应用防抖
      const debounceConfig = optimizationOptions.debounce
      if (
        debounceConfig?.enabled &&
        (!debounceConfig.apiNames || debounceConfig.apiNames.includes(apiName))
      ) {
        optimizedFn = createDebouncedApi(
          apiName,
          optimizedFn,
          debounceConfig.delay
        )
      }

      // 应用节流
      const throttleConfig = optimizationOptions.throttle
      if (
        throttleConfig?.enabled &&
        (!throttleConfig.apiNames || throttleConfig.apiNames.includes(apiName))
      ) {
        optimizedFn = createThrottledApi(
          apiName,
          optimizedFn,
          throttleConfig.delay
        )
      }

      // 应用批量处理
      if (optimizationOptions.batch?.enabled) {
        const batchedFn = (context: ApiExecutionContext) => {
          return addToBatch(apiName, context)
        }
        optimizedFn = batchedFn
      }

      result[apiName] = optimizedFn
    })

    return result
  })

  /**
   * 清理优化函数缓存
   */
  const clearOptimizationCache = () => {
    // 清理防抖函数
    debouncedFunctions.forEach((fn) => {
      if (typeof fn === 'function' && 'cancel' in fn) {
        ;(fn as any).cancel()
      }
    })
    debouncedFunctions.clear()

    // 清理节流函数
    throttledFunctions.forEach((fn) => {
      if (typeof fn === 'function' && 'cancel' in fn) {
        ;(fn as any).cancel()
      }
    })
    throttledFunctions.clear()

    // 清理批量队列
    batchQueue.value = []
    if (batchTimer) {
      clearTimeout(batchTimer)
      batchTimer = null
    }
  }

  /**
   * 获取性能统计
   */
  const getPerformanceStats = () => {
    return {
      debouncedApis: Array.from(debouncedFunctions.keys()),
      throttledApis: Array.from(throttledFunctions.keys()),
      batchQueueSize: batchQueue.value.length,
      cacheSize: baseApiEvents.apiStates.value
        ? Object.keys(baseApiEvents.apiStates.value).length
        : 0,
    }
  }

  // 组件挂载时预加载
  preloadApis()

  // 组件卸载时清理
  onUnmounted(() => {
    clearOptimizationCache()
  })

  return {
    // 优化后的 API 事件
    dynamicApiEvents: optimizedApiEvents,

    // 继承基础功能
    ...baseApiEvents,

    // 优化相关方法
    clearOptimizationCache,
    getPerformanceStats,
    preloadApis,

    // 批量处理
    processBatchQueue: () => processBatchQueue(),
    getBatchQueueSize: () => batchQueue.value.length,
  }
}
