/**
 * 客户端工厂
 * 提供创建各种客户端的便捷方法
 */

import { ODataClient } from '../clients/odata-client'
import { HttpClient, HttpClientConfig } from '../clients/http-client'
import type { ApiClient, IODataClient, ODataClientConfig } from '../core/types'

// 客户端缓存
const odataClientCache = new Map<string, IODataClient>()
const httpClientCache = new Map<string, ApiClient>()

/**
 * 创建 OData 客户端
 */
export function createODataClient(config: ODataClientConfig): IODataClient {
  return new ODataClient(config)
}

/**
 * 获取或创建缓存的 OData 客户端
 */
export function getOrCreateODataClient(
  config: ODataClientConfig
): IODataClient {
  const cacheKey = `${config.baseUrl}:${config.entitySet || 'default'}`
  if (!odataClientCache.has(cacheKey)) {
    odataClientCache.set(cacheKey, new ODataClient(config))
  }

  return odataClientCache.get(cacheKey)!
}

/**
 * 创建 HTTP 客户端
 */
export function createHttpClient(config?: HttpClientConfig): ApiClient {
  return new HttpClient(config)
}

/**
 * 获取或创建缓存的 HTTP 客户端
 */
export function getOrCreateHttpClient(
  config: HttpClientConfig = {}
): ApiClient {
  const cacheKey = `${config.baseURL || 'default'}:${config.timeout || 30000}`

  if (!httpClientCache.has(cacheKey)) {
    httpClientCache.set(cacheKey, new HttpClient(config))
  }

  return httpClientCache.get(cacheKey)!
}

/**
 * 清除 OData 客户端缓存
 */
export function clearODataClientCache(): void {
  odataClientCache.clear()
}

/**
 * 清除 HTTP 客户端缓存
 */
export function clearHttpClientCache(): void {
  httpClientCache.clear()
}

/**
 * 清除所有客户端缓存
 */
export function clearAllClientCache(): void {
  clearODataClientCache()
  clearHttpClientCache()
}

/**
 * 预配置的客户端工厂
 */
export class ClientFactory {
  private defaultODataConfig: Partial<ODataClientConfig> = {}
  private defaultHttpConfig: Partial<HttpClientConfig> = {}

  /**
   * 设置默认 OData 配置
   */
  setDefaultODataConfig(config: Partial<ODataClientConfig>): void {
    this.defaultODataConfig = { ...config }
  }

  /**
   * 设置默认 HTTP 配置
   */
  setDefaultHttpConfig(config: Partial<HttpClientConfig>): void {
    this.defaultHttpConfig = { ...config }
  }

  /**
   * 创建 OData 客户端（使用默认配置）
   */
  createODataClient(config: ODataClientConfig): IODataClient {
    const mergedConfig = { ...this.defaultODataConfig, ...config }
    return new ODataClient(mergedConfig as ODataClientConfig)
  }

  /**
   * 创建 HTTP 客户端（使用默认配置）
   */
  createHttpClient(config?: HttpClientConfig): ApiClient {
    const mergedConfig = { ...this.defaultHttpConfig, ...config }
    return new HttpClient(mergedConfig)
  }

  /**
   * 批量创建 OData 客户端
   */
  createODataClients(configs: ODataClientConfig[]): IODataClient[] {
    return configs.map((config) => this.createODataClient(config))
  }

  /**
   * 批量创建 HTTP 客户端
   */
  createHttpClients(configs: HttpClientConfig[]): ApiClient[] {
    return configs.map((config) => this.createHttpClient(config))
  }
}

// 默认客户端工厂实例
let defaultClientFactory: ClientFactory | null = null

/**
 * 获取默认客户端工厂实例
 */
export function getDefaultClientFactory(): ClientFactory {
  if (!defaultClientFactory) {
    defaultClientFactory = new ClientFactory()
  }
  return defaultClientFactory
}

/**
 * 设置默认客户端工厂实例
 */
export function setDefaultClientFactory(factory: ClientFactory): void {
  defaultClientFactory = factory
}

/**
 * 创建客户端工厂实例
 */
export function createClientFactory(): ClientFactory {
  return new ClientFactory()
}
