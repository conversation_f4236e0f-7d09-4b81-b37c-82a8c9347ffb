/**
 * 核心接口定义
 * 定义系统的核心抽象接口，遵循接口隔离原则
 */

import type { ApiResponse, RequestContext } from './types'

/**
 * 请求执行器接口
 * 负责执行具体的请求逻辑
 */
export interface RequestExecutor<TConfig = any> {
  /**
   * 执行请求
   * @param config 请求配置
   * @param context 请求上下文
   */
  execute(config: TConfig, context?: RequestContext): Promise<ApiResponse>
}

/**
 * 配置标准化器接口
 * 负责将不同格式的配置标准化
 */
export interface ConfigNormalizer<TInput = any, TOutput = any> {
  /**
   * 标准化配置
   * @param config 输入配置
   */
  normalize(config: TInput): TOutput

  /**
   * 检查是否可以处理该配置
   * @param config 输入配置
   */
  canHandle(config: TInput): boolean
}

/**
 * 协议适配器接口
 * 负责特定协议的请求处理
 */
export interface ProtocolAdapter<TConfig = any> extends RequestExecutor<TConfig> {
  /**
   * 协议名称
   */
  readonly protocol: string

  /**
   * 检查是否支持该配置
   * @param config 请求配置
   */
  supports(config: TConfig): boolean
}

/**
 * 客户端工厂接口
 * 负责创建和管理客户端实例
 */
export interface ClientFactory<TConfig = any, TClient = any> {
  /**
   * 创建客户端
   * @param config 客户端配置
   */
  create(config: TConfig): TClient

  /**
   * 获取或创建客户端（带缓存）
   * @param config 客户端配置
   */
  getOrCreate(config: TConfig): TClient

  /**
   * 清除缓存
   */
  clearCache(): void
}

/**
 * 表达式解析器接口
 * 负责解析模板表达式
 */
export interface ExpressionParser {
  /**
   * 解析表达式
   * @param template 模板
   * @param context 上下文
   */
  parse(template: any, context: RequestContext): any
}

/**
 * 错误处理器接口
 * 负责统一的错误处理
 */
export interface ErrorHandler {
  /**
   * 处理错误
   * @param error 错误信息
   */
  handle(error: Error | string): void
}

/**
 * 中间件接口
 * 支持请求/响应的中间件处理
 */
export interface Middleware<TConfig = any> {
  /**
   * 中间件名称
   */
  readonly name: string

  /**
   * 请求前处理
   * @param config 请求配置
   * @param context 请求上下文
   */
  beforeRequest?(config: TConfig, context: RequestContext): Promise<TConfig> | TConfig

  /**
   * 响应后处理
   * @param response 响应数据
   * @param config 请求配置
   * @param context 请求上下文
   */
  afterResponse?(
    response: ApiResponse,
    config: TConfig,
    context: RequestContext
  ): Promise<ApiResponse> | ApiResponse

  /**
   * 错误处理
   * @param error 错误
   * @param config 请求配置
   * @param context 请求上下文
   */
  onError?(
    error: Error,
    config: TConfig,
    context: RequestContext
  ): Promise<ApiResponse> | ApiResponse | void
}

/**
 * 插件接口
 * 支持功能扩展的插件系统
 */
export interface Plugin {
  /**
   * 插件名称
   */
  readonly name: string

  /**
   * 插件版本
   */
  readonly version?: string

  /**
   * 安装插件
   * @param context 安装上下文
   */
  install(context: PluginContext): void | Promise<void>

  /**
   * 卸载插件
   */
  uninstall?(): void | Promise<void>
}

/**
 * 插件上下文接口
 * 提供插件安装时的上下文信息
 */
export interface PluginContext {
  /**
   * 注册协议适配器
   * @param adapter 适配器
   */
  registerAdapter(adapter: ProtocolAdapter): void

  /**
   * 注册中间件
   * @param middleware 中间件
   */
  registerMiddleware(middleware: Middleware): void

  /**
   * 注册配置标准化器
   * @param normalizer 标准化器
   */
  registerNormalizer(normalizer: ConfigNormalizer): void

  /**
   * 获取服务实例
   * @param name 服务名称
   */
  getService<T = any>(name: string): T | undefined

  /**
   * 注册服务
   * @param name 服务名称
   * @param service 服务实例
   */
  registerService<T = any>(name: string, service: T): void
}

/**
 * 服务容器接口
 * 提供依赖注入和服务管理
 */
export interface ServiceContainer {
  /**
   * 注册服务
   * @param name 服务名称
   * @param factory 服务工厂函数
   * @param singleton 是否单例
   */
  register<T = any>(
    name: string,
    factory: () => T,
    singleton?: boolean
  ): void

  /**
   * 获取服务
   * @param name 服务名称
   */
  get<T = any>(name: string): T

  /**
   * 检查服务是否存在
   * @param name 服务名称
   */
  has(name: string): boolean

  /**
   * 清除所有服务
   */
  clear(): void
}
