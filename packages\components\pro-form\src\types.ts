import { ValueEnum, ValueTypeEnum } from '@neue-plus/components/pro-table'
import type { FormItemRule } from 'element-plus'
import type { ExtractPropTypes, PropType } from 'vue'

export type FieldProp = string | string[]

export type FieldSize = '' | 'default' | 'small' | 'large'

export type ValidateStatus = '' | 'success' | 'error' | 'validating'

export interface FormFieldRule {
  required?: boolean
  message?: string
  trigger?: 'blur' | 'change' | string
  type?: string
  validator?: (...args: any[]) => boolean | Promise<boolean>
  [key: string]: any
}

export interface ProFormItemProps {
  prop: string
  label: string
  placeholder?: string
  valueType?: ValueTypeEnum
  valueEnum?: ValueEnum
  // 显示控制
  show?: boolean
  disabled?: boolean
  rules?: FormItemRule[]
  span?: number
  offset?: number
  required?: boolean
}

export type ButtonAlign = 'center' | 'start' | 'end'
export const proFormProps = {
  // 表单配置
  formItems: {
    type: Array as () => ProFormItemProps[],
    required: true,
  },
  buttonAlign: {
    type: String as () => ButtonAlign,
    default: 'center',
  },
  span: {
    type: Number,
    default: 12,
  },
  submitButtonProps: {
    type: [Object, Boolean] as PropType<Record<string, any> | false>,
    default: () => ({
      type: 'primary',
    }),
  },
  resetButtonProps: {
    type: [Object, Boolean] as PropType<Record<string, any> | false>,
    default: () => ({
      type: 'primary',
    }),
  },
  gutter: {
    type: [Number, Array] as PropType<number | number[]>,
    default: 16,
  },

  // 表单数据
  modelValue: {
    type: Object as () => Record<string, any>,
    default: () => ({}),
  },
  inline: {
    type: Boolean,
    default: false,
  },
  labelWidth: {
    type: [Number, String] as PropType<number | 'auto'>,
    default: '120px',
  },
  // 表单大小
  size: {
    type: String as () => FieldSize,
    default: 'default',
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
}

export type ProFormProps = ExtractPropTypes<typeof proFormProps>
// 事件类型
export type ProFormEmits = {
  (e: 'update:modelValue', value: Record<string, any>): void
  (e: 'submit', value: Record<string, any>): void
  (e: 'reset'): void
  (e: 'cancel'): void
  (e: 'change', prop: string, value: any): void
  (e: 'validate', prop: string, isValid: boolean, message: string): void
}
