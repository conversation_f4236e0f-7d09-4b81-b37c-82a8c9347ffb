/**
 * OData 客户端实现
 * 基于 @odata/client 框架的封装，提供完整的 OData v4 协议支持
 */

import { OData, ODataKeyPredicate, SystemQueryOptions } from '@odata/client'
import { ODataV4 } from '@odata/client/lib/types_v4'
import { parseExpression } from '../core/expression-parser'
import { ServiceError } from '../core/error-handler'
import type {
  ApiResponse,
  AuthConfig,
  IODataClient,
  ODataClientConfig,
  ODataEntity,
  ODataQueryOptions,
  RequestContext,
} from '../core/types'

export class ODataClient implements IODataClient {
  private odataClient: ODataV4
  private config: ODataClientConfig
  private authHeaders: Record<string, string> = {}

  constructor(config: ODataClientConfig) {
    this.config = config

    // 创建 OData 客户端实例
    this.odataClient = OData.New4({
      serviceEndpoint: config.baseUrl,
    })

    // 设置认证
    if (config.auth) {
      this.setupAuthentication(config.auth)
    }
  }

  /**
   * 设置认证
   */
  private setupAuthentication(auth: AuthConfig): void {
    switch (auth.type) {
      case 'basic':
        if (auth.credentials?.username && auth.credentials?.password) {
          const credentials = btoa(
            `${auth.credentials.username}:${auth.credentials.password}`
          )
          this.authHeaders['Authorization'] = `Basic ${credentials}`
        }
        break
      case 'bearer':
        if (auth.credentials?.token) {
          this.authHeaders['Authorization'] = `Bearer ${auth.credentials.token}`
        }
        break
      case 'oauth2':
        if (auth.credentials?.token) {
          this.authHeaders['Authorization'] = `Bearer ${auth.credentials.token}`
        }
        break
    }
  }

  /**
   * 获取实体集名称
   */
  private getEntitySetName(entitySet?: string): string {
    const targetEntitySet = entitySet || this.config.entitySet
    if (!targetEntitySet) {
      throw new ServiceError('Entity set is required', 'MISSING_ENTITY_SET')
    }
    return targetEntitySet
  }

  /**
   * 创建带认证头的请求配置
   */
  // private createRequestConfig(config: ApiConfig) {
  //   const headers = {
  //     ...this.config.headers,
  //     ...this.authHeaders,
  //     ...config.headers,
  //   }

  //   return {
  //     ...config,
  //     headers: Object.keys(headers).length > 0 ? headers : undefined,
  //   }
  // }

  /**
   * 通用请求方法（向后兼容）
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async request<T = any>(config: any): Promise<T> {
    // 这个方法主要用于向后兼容，实际使用 @odata/client 的方法
    throw new ServiceError(
      'Use specific OData methods instead of generic request',
      'DEPRECATED_METHOD'
    )
  }

  /**
   * 构建 OData 查询参数
   */
  private buildODataParams(
    options: ODataQueryOptions,
    context: RequestContext = {}
  ) {
    console.log(options, context)
    // @ts-ignore - newParam() is deprecated but still functional, TODO: migrate to new API
    const params = this.odataClient.newParam()
    if (options.filter) {
      const filterValue = parseExpression(options.filter, context)
      params.filter(filterValue)
    }
    if (options.select) {
      params.select(options.select)
    }
    if (options.expand) {
      params.expand(options.expand)
    }
    if (options.orderby) {
      params.orderby(options.orderby)
    }
    if (options.top !== undefined) {
      params.top(options.top)
    }
    if (options.skip !== undefined) {
      params.skip(options.skip)
    }
    // if (options.count) {
    // }
    params.count(true)
    if (options.search) {
      params.search(options.search)
    }
    return params
  }

  /**
   * 查询实体集合
   */
  async query<T = ODataEntity>(
    entitySet: string,
    options: ODataQueryOptions = {},
    entity: Partial<T>
  ): Promise<ApiResponse<T>> {
    try {
      const targetEntitySet = this.getEntitySetName(entitySet)
      const params = this.buildODataParams(options, entity)
      // const requestConfig = this.createRequestConfig(config)
      const response = await this.odataClient.newRequest({
        collection: targetEntitySet,
        params,
      })
      return {
        data: response as any,
        success: true,
      }
    } catch (error) {
      console.error('OData query failed:', error)
      return {
        data: null as any,
        success: false,
        error: error instanceof Error ? error.message : 'Query failed',
      }
    }
  }
  /**
   * 根据键获取单个实体
   */
  async get<T = ODataEntity>(
    entitySet: string,
    key: ODataKeyPredicate,
    params?: SystemQueryOptions
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.odataClient.newRequest({
        collection: `${entitySet}`,
        id: key,
        params,
      })

      return {
        data: response as T,
        success: true,
      }
    } catch (error) {
      console.error('OData get failed:', error)
      return {
        data: null as any,
        success: false,
        error: error instanceof Error ? error.message : 'Get operation failed',
      }
    }
  }

  /**
   * 创建新实体
   */
  async create<T = ODataEntity>(
    entitySet: string,
    entity?: Partial<T>
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.odataClient.newRequest({
        method: 'POST',
        collection: `${entitySet}`,
        entity,
      })

      return {
        data: response as T,
        success: true,
      }
    } catch (error) {
      return {
        data: null as any,
        success: false,
        error:
          error instanceof Error ? error.message : 'Create operation failed',
      }
    }
  }

  /**
   * 更新实体
   */
  async update<T = ODataEntity>(
    entitySet: string,
    key: ODataKeyPredicate,
    entity: Partial<T>
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.odataClient.newRequest({
        method: 'PUT',
        collection: `${entitySet}`,
        id: key,
        entity,
      })
      return {
        data: response as T,
        success: true,
      }
    } catch (error) {
      console.error('OData update failed:', error)
      return {
        data: null as any,
        success: false,
        error:
          error instanceof Error ? error.message : 'Update operation failed',
      }
    }
  }

  /**
   * 部分更新实体
   */
  async patch<T = ODataEntity>(
    entitySet: string,
    key: ODataKeyPredicate,
    entity?: Partial<T>
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.odataClient.newRequest({
        method: 'PATCH',
        collection: `${entitySet}`,
        id: key,
        entity,
      })

      return {
        data: response as T,
        success: true,
      }
    } catch (error) {
      console.error('OData patch failed:', error)
      return {
        data: null as any,
        success: false,
        error:
          error instanceof Error ? error.message : 'Patch operation failed',
      }
    }
  }

  /**
   * 删除实体
   */
  async delete(
    entitySet: string,
    key: ODataKeyPredicate
  ): Promise<ApiResponse<any>> {
    try {
      await this.odataClient.newRequest({
        method: 'DELETE',
        collection: `${entitySet}`,
        id: key,
      })

      return {
        data: null,
        success: true,
      }
    } catch (error) {
      console.error('OData delete failed:', error)
      return {
        data: null,
        success: false,
        error:
          error instanceof Error ? error.message : 'Delete operation failed',
      }
    }
  }

  /**
   * 执行函数调用
   */
  async callFunction<T = any>(
    functionName: string,
    parameters: Record<string, any> = {},
    context: RequestContext = {}
  ): Promise<T | null> {
    try {
      const parsedParams = parseExpression(parameters, context)
      console.log('OData function call:', functionName, parsedParams)

      // 暂时返回 null，等待 @odata/client API 确认
      return null
    } catch (error) {
      console.error('OData function call failed:', error)
      return null
    }
  }

  /**
   * 执行操作调用
   */
  async callAction<T = any>(
    actionName: string,
    parameters: Record<string, any> = {},
    context: RequestContext = {}
  ): Promise<T | null> {
    try {
      const parsedParams = parseExpression(parameters, context)
      console.log('OData action call:', actionName, parsedParams)

      // 暂时返回 null，等待 @odata/client API 确认
      return null
    } catch (error) {
      console.error('OData action call failed:', error)
      return null
    }
  }

  /**
   * 获取服务元数据
   */
  async getMetadata(): Promise<string | null> {
    try {
      console.log('Getting OData metadata')

      // 暂时返回 null，等待 @odata/client API 确认
      return null
    } catch (error) {
      console.error('Failed to get OData metadata:', error)
      return null
    }
  }
}
