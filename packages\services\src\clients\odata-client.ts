/**
 * OData 客户端实现
 * 基于 @odata/client 框架的封装，提供完整的 OData v4 协议支持
 */

import { OData } from '@odata/client'
import { ODataV4 } from '@odata/client/lib/types_v4'
import { parseExpression } from '../core/expression-parser'
import { ServiceError } from '../core/error-handler'
import type {
  ApiResponse,
  AuthConfig,
  IODataClient,
  ODataClientConfig,
  ODataEntity,
  ODataQueryOptions,
  RequestContext,
} from '../core/types'

export class ODataClient implements IODataClient {
  private odataClient: ODataV4
  private config: ODataClientConfig
  private authHeaders: Record<string, string> = {}

  constructor(config: ODataClientConfig) {
    this.config = config

    // 创建 OData 客户端实例
    this.odataClient = OData.New4({
      serviceEndpoint: config.baseUrl,
    })

    // 设置认证
    if (config.auth) {
      this.setupAuthentication(config.auth)
    }
  }

  /**
   * 设置认证
   */
  private setupAuthentication(auth: AuthConfig): void {
    switch (auth.type) {
      case 'basic':
        if (auth.credentials?.username && auth.credentials?.password) {
          const credentials = btoa(
            `${auth.credentials.username}:${auth.credentials.password}`
          )
          this.authHeaders['Authorization'] = `Basic ${credentials}`
        }
        break
      case 'bearer':
        if (auth.credentials?.token) {
          this.authHeaders['Authorization'] = `Bearer ${auth.credentials.token}`
        }
        break
      case 'oauth2':
        if (auth.credentials?.token) {
          this.authHeaders['Authorization'] = `Bearer ${auth.credentials.token}`
        }
        break
    }
  }

  /**
   * 获取实体集名称
   */
  private getEntitySetName(entitySet?: string): string {
    const targetEntitySet = entitySet || this.config.entitySet
    if (!targetEntitySet) {
      throw new ServiceError('Entity set is required', 'MISSING_ENTITY_SET')
    }
    return targetEntitySet
  }

  /**
   * 创建带认证头的请求配置
   */
  private createRequestConfig(config: any): any {
    const headers = {
      ...this.config.headers,
      ...this.authHeaders,
      ...config.headers,
    }

    return {
      ...config,
      headers: Object.keys(headers).length > 0 ? headers : undefined,
    }
  }

  /**
   * 通用请求方法（向后兼容）
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async request<T = any>(config: any): Promise<T> {
    // 这个方法主要用于向后兼容，实际使用 @odata/client 的方法
    throw new ServiceError(
      'Use specific OData methods instead of generic request',
      'DEPRECATED_METHOD'
    )
  }

  /**
   * 构建 OData 查询参数
   */
  private buildODataParams(
    options: ODataQueryOptions,
    context: RequestContext = {}
  ) {
    // @ts-ignore - newParam() is deprecated but still functional, TODO: migrate to new API
    const params = this.odataClient.newParam()
    if (options.filter) {
      const filterValue = parseExpression(options.filter, context)
      params.filter(filterValue)
    }
    if (options.select) {
      params.select(options.select)
    }
    if (options.expand) {
      params.expand(options.expand)
    }
    if (options.orderby) {
      params.orderby(options.orderby)
    }
    if (options.top !== undefined) {
      params.top(options.top)
    }
    if (options.skip !== undefined) {
      params.skip(options.skip)
    }
    if (options.count) {
      params.inlinecount()
    }
    if (options.search) {
      params.search(options.search)
    }

    console.log('123456', options, params)
    return params
  }

  /**
   * 查询实体集合
   */
  async query<T = ODataEntity>(
    entitySet?: string,
    options: ODataQueryOptions = {},
    context: RequestContext = {}
  ): Promise<ApiResponse<T>> {
    try {
      const targetEntitySet = this.getEntitySetName(entitySet)
      const params = this.buildODataParams(options, context)

      // 使用 @odata/client 的 newRequest API
      const response = await this.odataClient.newRequest({
        collection: targetEntitySet,
        params,
      })

      // 处理响应数据
      const responseData = response as any
      const data =
        responseData.value || responseData.d?.results || responseData || []
      const total =
        responseData['@odata.count'] ||
        responseData.d?.__count ||
        data.length ||
        0
      const pageSize = options.top || 20
      const currentPage = Math.floor((options.skip || 0) / pageSize) + 1

      return {
        data: data as any,
        success: true,
        total,
        page: currentPage,
        pageSize,
        hasNext: !!responseData['@odata.nextLink'],
        hasPrev: (options.skip || 0) > 0,
      }
    } catch (error) {
      console.error('OData query failed:', error)
      return {
        data: [] as any,
        success: false,
        total: 0,
        page: 1,
        pageSize: options.top || 20,
        hasNext: false,
        hasPrev: false,
        error: error instanceof Error ? error.message : 'Query failed',
      }
    }
  }

  /**
   * 根据键获取单个实体
   */
  async get<T = ODataEntity>(
    key: string | number,
    entitySet?: string,
    options: Pick<ODataQueryOptions, 'select' | 'expand'> = {},
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    context: RequestContext = {}
  ): Promise<T | null> {
    try {
      const targetEntitySet = this.getEntitySetName(entitySet)
      // @ts-ignore - newParam() is deprecated but still functional, TODO: migrate to new API
      const params = this.odataClient.newParam()

      if (options.select) {
        params.select(options.select)
      }
      if (options.expand) {
        params.expand(options.expand)
      }

      // 使用 newRequest API 获取单个实体
      const keyValue = typeof key === 'string' ? `'${key}'` : key
      console.log(targetEntitySet, keyValue)
      const response = await this.odataClient.newRequest({
        collection: `${targetEntitySet}(${keyValue})`,
        params,
      })

      return response as T
    } catch (error) {
      console.error('OData get failed:', error)
      return null
    }
  }

  /**
   * 创建新实体
   */
  async create<T = ODataEntity>(
    entity: Partial<T>,
    entitySet?: string
  ): Promise<T | null> {
    try {
      const targetEntitySet = this.getEntitySetName(entitySet)
      console.log('OData create:', targetEntitySet, entity)

      // 暂时返回 null，等待正确的 @odata/client API 实现
      return null
    } catch (error) {
      console.error('OData create failed:', error)
      return null
    }
  }

  /**
   * 更新实体
   */
  async update<T = ODataEntity>(
    key: string | number,
    entity: Partial<T>,
    entitySet?: string
  ): Promise<T | null> {
    try {
      const targetEntitySet = this.getEntitySetName(entitySet)
      console.log('OData update:', targetEntitySet, key, entity)

      // 暂时返回 null，等待正确的 @odata/client API 实现
      return null
    } catch (error) {
      console.error('OData update failed:', error)
      return null
    }
  }

  /**
   * 部分更新实体
   */
  async patch<T = ODataEntity>(
    key: string | number,
    entity: Partial<T>,
    entitySet?: string
  ): Promise<T | null> {
    try {
      const targetEntitySet = this.getEntitySetName(entitySet)
      console.log('OData patch:', targetEntitySet, key, entity)

      // 暂时返回 null，等待正确的 @odata/client API 实现
      return null
    } catch (error) {
      console.error('OData patch failed:', error)
      return null
    }
  }

  /**
   * 删除实体
   */
  async delete(key: string | number, entitySet?: string): Promise<boolean> {
    try {
      const targetEntitySet = this.getEntitySetName(entitySet)
      console.log('OData delete:', targetEntitySet, key)

      // 暂时返回 false，等待正确的 @odata/client API 实现
      return false
    } catch (error) {
      console.error('OData delete failed:', error)
      return false
    }
  }

  /**
   * 执行函数调用
   */
  async callFunction<T = any>(
    functionName: string,
    parameters: Record<string, any> = {},
    context: RequestContext = {}
  ): Promise<T | null> {
    try {
      const parsedParams = parseExpression(parameters, context)
      console.log('OData function call:', functionName, parsedParams)

      // 暂时返回 null，等待 @odata/client API 确认
      return null
    } catch (error) {
      console.error('OData function call failed:', error)
      return null
    }
  }

  /**
   * 执行操作调用
   */
  async callAction<T = any>(
    actionName: string,
    parameters: Record<string, any> = {},
    context: RequestContext = {}
  ): Promise<T | null> {
    try {
      const parsedParams = parseExpression(parameters, context)
      console.log('OData action call:', actionName, parsedParams)

      // 暂时返回 null，等待 @odata/client API 确认
      return null
    } catch (error) {
      console.error('OData action call failed:', error)
      return null
    }
  }

  /**
   * 获取服务元数据
   */
  async getMetadata(): Promise<string | null> {
    try {
      console.log('Getting OData metadata')

      // 暂时返回 null，等待 @odata/client API 确认
      return null
    } catch (error) {
      console.error('Failed to get OData metadata:', error)
      return null
    }
  }
}
