/**
 * OData API 适配器
 * 处理 OData 协议的 API 请求
 */

import { ODataClient } from '../clients/odata-client'
import { parseExpression } from '../core/expression-parser'
import type { ProtocolAdapter } from '../core/interfaces'
import type {
  ApiConfig,
  ApiResponse,
  ODataApiConfig,
  ODataClientConfig,
  RequestContext,
} from '../core/types'

export class ODataAdapter implements ProtocolAdapter<ApiConfig> {
  readonly protocol = 'odata'
  private clientCache = new Map<string, ODataClient>()

  /**
   * 检查是否支持该配置
   */
  supports(config: ApiConfig): boolean {
    return config.protocol === 'odata'
  }

  /**
   * 执行 OData API 请求
   */
  async execute(
    config: ApiConfig,
    context: RequestContext = {}
  ): Promise<ApiResponse> {
    const odataConfig = config as ODataApiConfig

    try {
      // 从 URL 中提取 baseUrl 和 entitySet
      const { baseUrl, entitySet } = this.parseODataUrl(odataConfig.url)

      // 获取或创建 OData 客户端
      const client = this.getOrCreateClient(baseUrl, {
        baseUrl,
        headers: odataConfig.headers,
        timeout: odataConfig.timeout,
      })

      // 根据 HTTP 方法执行相应操作
      return await this.executeByMethod(client, odataConfig, entitySet, context)
    } catch (error) {
      return {
        data: null,
        success: false,
        total: 0,
        page: 1,
        pageSize: 0,
        hasNext: false,
        hasPrev: false,
        error: error instanceof Error ? error.message : 'OData request failed',
      }
    }
  }

  /**
   * 解析 OData URL
   */
  private parseODataUrl(url: string): { baseUrl: string; entitySet: string } {
    const urlParts = url.split('/')
    const baseUrl = urlParts.slice(0, -1).join('/')
    const entitySet = urlParts[urlParts.length - 1]

    return { baseUrl, entitySet }
  }

  /**
   * 获取或创建 OData 客户端
   */
  private getOrCreateClient(
    baseUrl: string,
    config: ODataClientConfig
  ): ODataClient {
    const cacheKey = baseUrl

    if (!this.clientCache.has(cacheKey)) {
      this.clientCache.set(cacheKey, new ODataClient(config))
    }

    return this.clientCache.get(cacheKey)!
  }

  /**
   * 根据 HTTP 方法执行相应操作
   */
  private async executeByMethod(
    client: ODataClient,
    config: ODataApiConfig,
    entitySet: string,
    context: RequestContext
  ): Promise<ApiResponse> {
    switch (config.method.toLowerCase()) {
      case 'get':
        return this.handleGet(client, config, entitySet, context)
      case 'post':
        return this.handlePost(client, config, entitySet, context)
      case 'put':
        return this.handlePut(client, config, entitySet, context)
      case 'patch':
        return this.handlePatch(client, config, entitySet, context)
      case 'delete':
        return this.handleDelete(client, config, entitySet, context)
      default:
        throw new Error(`Unsupported HTTP method: ${config.method}`)
    }
  }

  /**
   * 处理 GET 请求
   */
  private async handleGet(
    client: ODataClient,
    config: ODataApiConfig,
    entitySet: string,
    context: RequestContext
  ): Promise<ApiResponse> {
    if (config.query) {
      const result = await client.query(entitySet, config.query, context)

      // 如果指定了数据路径，从响应中提取数据
      if (config.dataPath) {
        const extractedData = this.extractDataByPath(result, config.dataPath)
        return {
          ...result,
          data: extractedData,
        }
      }

      return result
    }

    // 如果没有查询选项，可能是获取单个实体
    if (config.params?.id) {
      const parsedParams = parseExpression(config.params, context)
      const entity = await client.get(parsedParams.id, entitySet)

      return {
        data: entity,
        success: !!entity,
        total: entity ? 1 : 0,
        page: 1,
        pageSize: 1,
        hasNext: false,
        hasPrev: false,
      }
    }

    // 默认查询所有
    return client.query(entitySet, {}, context)
  }

  /**
   * 处理 POST 请求
   */
  private async handlePost(
    client: ODataClient,
    config: ODataApiConfig,
    entitySet: string,
    context: RequestContext
  ): Promise<ApiResponse> {
    if (!config.body) {
      throw new Error('POST request requires body')
    }

    const parsedBody = parseExpression(config.body, context)
    const result = await client.create(parsedBody, entitySet)

    return {
      data: result,
      success: !!result,
      total: result ? 1 : 0,
      page: 1,
      pageSize: 1,
      hasNext: false,
      hasPrev: false,
    }
  }

  /**
   * 处理 PUT 请求
   */
  private async handlePut(
    client: ODataClient,
    config: ODataApiConfig,
    entitySet: string,
    context: RequestContext
  ): Promise<ApiResponse> {
    if (!config.body || !config.params?.id) {
      throw new Error('PUT request requires body and id parameter')
    }

    const parsedBody = parseExpression(config.body, context)
    const parsedParams = parseExpression(config.params, context)
    const result = await client.update(parsedParams.id, parsedBody, entitySet)

    return {
      data: result,
      success: !!result,
      total: result ? 1 : 0,
      page: 1,
      pageSize: 1,
      hasNext: false,
      hasPrev: false,
    }
  }

  /**
   * 处理 PATCH 请求
   */
  private async handlePatch(
    client: ODataClient,
    config: ODataApiConfig,
    entitySet: string,
    context: RequestContext
  ): Promise<ApiResponse> {
    if (!config.body || !config.params?.id) {
      throw new Error('PATCH request requires body and id parameter')
    }

    const parsedBody = parseExpression(config.body, context)
    const parsedParams = parseExpression(config.params, context)
    const result = await client.patch(parsedParams.id, parsedBody, entitySet)

    return {
      data: result,
      success: !!result,
      total: result ? 1 : 0,
      page: 1,
      pageSize: 1,
      hasNext: false,
      hasPrev: false,
    }
  }

  /**
   * 处理 DELETE 请求
   */
  private async handleDelete(
    client: ODataClient,
    config: ODataApiConfig,
    entitySet: string,
    context: RequestContext
  ): Promise<ApiResponse> {
    if (!config.params?.id) {
      throw new Error('DELETE request requires id parameter')
    }

    const parsedParams = parseExpression(config.params, context)
    const result = await client.delete(parsedParams.id, entitySet)

    return {
      data: null,
      success: result,
      total: 0,
      page: 1,
      pageSize: 0,
      hasNext: false,
      hasPrev: false,
    }
  }

  /**
   * 根据路径提取数据
   */
  private extractDataByPath(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined
    }, obj)
  }

  /**
   * 清除客户端缓存
   */
  clearCache(): void {
    this.clientCache.clear()
  }

  /**
   * 获取缓存的客户端数量
   */
  getCacheSize(): number {
    return this.clientCache.size
  }
}

// 默认 OData 适配器实例
let defaultODataAdapter: ODataAdapter | null = null

/**
 * 获取默认 OData 适配器实例
 */
export function getDefaultODataAdapter(): ODataAdapter {
  if (!defaultODataAdapter) {
    defaultODataAdapter = new ODataAdapter()
  }
  return defaultODataAdapter
}

/**
 * 设置默认 OData 适配器实例
 */
export function setDefaultODataAdapter(adapter: ODataAdapter): void {
  defaultODataAdapter = adapter
}

/**
 * 创建 OData 适配器实例
 */
export function createODataAdapter(): ODataAdapter {
  return new ODataAdapter()
}
