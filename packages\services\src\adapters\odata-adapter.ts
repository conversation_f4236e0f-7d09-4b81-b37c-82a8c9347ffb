/**
 * OData API 适配器
 * 处理 OData 协议的 API 请求
 */

import { ODataClient } from '../clients/odata-client'
import { DataType, ResponseProcessor } from '../core/response-processor'
import type { ProtocolAdapter } from '../core/interfaces'
import type {
  ApiConfig,
  ApiResponse,
  ODataApiConfig,
  ODataClientConfig,
  RequestContext,
} from '../core/types'

export class ODataAdapter implements ProtocolAdapter<ApiConfig> {
  readonly protocol = 'odata'
  private clientCache = new Map<string, ODataClient>()

  /**
   * 检查是否支持该配置
   */
  supports(config: ApiConfig): boolean {
    return config.protocol === 'odata'
  }

  /**
   * 执行 OData API 请求
   */
  async execute(
    config: ApiConfig,
    context: RequestContext = {}
  ): Promise<ApiResponse> {
    const odataConfig = config as ODataApiConfig
    console.log(123456777)
    try {
      // 从 URL 中提取 baseUrl 和 entitySet
      const { baseUrl, entitySet } = this.parseODataUrl(odataConfig.url)
      console.log(baseUrl, entitySet)

      // 获取或创建 OData 客户端
      const client = this.getOrCreateClient(baseUrl, {
        baseUrl,
        headers: odataConfig.headers,
        timeout: odataConfig.timeout,
      })

      console.log(client, odataConfig, entitySet, context)
      // 根据 HTTP 方法执行相应操作
      return await this.executeByMethod(client, odataConfig, entitySet, context)
    } catch (error) {
      return {
        data: null,
        success: false,
        total: 0,
        page: 1,
        pageSize: 0,
        hasNext: false,
        hasPrev: false,
        error: error instanceof Error ? error.message : 'OData request failed',
      }
    }
  }

  /**
   * 解析 OData URL
   */
  private parseODataUrl(url: string): { baseUrl: string; entitySet: string } {
    const urlParts = url.split('/')
    const baseUrl = urlParts.slice(0, -1).join('/')
    const entitySet = urlParts[urlParts.length - 1]

    return { baseUrl: `${baseUrl}/`, entitySet }
  }

  /**
   * 获取或创建 OData 客户端
   */
  private getOrCreateClient(
    baseUrl: string,
    config: ODataClientConfig
  ): ODataClient {
    const cacheKey = baseUrl

    if (!this.clientCache.has(cacheKey)) {
      this.clientCache.set(cacheKey, new ODataClient(config))
    }

    console.log(123456, cacheKey, this.clientCache.get(cacheKey))
    return this.clientCache.get(cacheKey)!
  }

  /**
   * 根据 HTTP 方法执行相应操作
   */
  private async executeByMethod(
    client: ODataClient,
    config: ODataApiConfig,
    entitySet: string,
    context: RequestContext
  ): Promise<ApiResponse> {
    console.log(client, config, entitySet, context)
    switch (config.method.toLowerCase()) {
      case 'get':
        return this.handleGet(client, config, entitySet, context)
      case 'post':
        return this.handlePost(client, config, entitySet, context)
      case 'put':
        return this.handlePut(client, config, entitySet, context)
      case 'patch':
        return this.handlePatch(client, config, entitySet, context)
      case 'delete':
        return this.handleDelete(client, config, entitySet)
      default:
        throw new Error(`Unsupported HTTP method: ${config.method}`)
    }
  }

  private buildODataKey(key: Record<string, any>) {
    const entries = Object.entries(key)
    if (entries.length === 1) {
      return entries[0][1]
    } else {
      key
    }
  }
  /**
   * 处理 GET 请求
   */
  private async handleGet(
    client: ODataClient,
    config: ODataApiConfig,
    entitySet: string,
    context: RequestContext
  ): Promise<ApiResponse> {
    if (config.query) {
      // 执行查询
      const result = await client.query(entitySet, config.query, context)
      // 使用统一的响应处理器
      return ResponseProcessor.processGetResponse(result.data, config, {
        collectionPath: config.dataPath,
        forceDataType: DataType.COLLECTION,
      })
    } else {
      const key = this.buildODataKey(config.params)
      // 获取单个实体
      const result = await client.get(entitySet, key)
      // 使用统一的响应处理器
      return ResponseProcessor.processGetResponse(result.data, config, {
        forceDataType: DataType.SINGLE,
      })
    }
  }

  /**
   * 处理 POST 请求
   */
  private async handlePost(
    client: ODataClient,
    config: ODataApiConfig,
    entitySet: string,
    context: RequestContext
  ): Promise<ApiResponse> {
    const result = await client.create(entitySet, context)
    // 使用统一的响应处理器
    return ResponseProcessor.processPostResponse(result.data, config)
  }

  /**
   * 处理 PUT 请求
   */
  private async handlePut(
    client: ODataClient,
    config: ODataApiConfig,
    entitySet: string,
    context: RequestContext
  ): Promise<ApiResponse> {
    const key = this.buildODataKey(config.params)
    const result = await client.update(entitySet, key, context)

    // 使用统一的响应处理器
    return ResponseProcessor.processUpdateResponse(result.data, config)
  }

  /**
   * 处理 PATCH 请求
   */
  private async handlePatch(
    client: ODataClient,
    config: ODataApiConfig,
    entitySet: string,
    context: RequestContext
  ): Promise<ApiResponse> {
    const key = this.buildODataKey(config.params)
    const result = await client.patch(entitySet, key, context)

    // 使用统一的响应处理器
    return ResponseProcessor.processUpdateResponse(result.data, config)
  }

  /**
   * 处理 DELETE 请求
   */
  private async handleDelete(
    client: ODataClient,
    config: ODataApiConfig,
    entitySet: string
  ): Promise<ApiResponse> {
    const key = this.buildODataKey(config.params)
    const result = await client.delete(entitySet, key)

    // 使用统一的响应处理器
    return ResponseProcessor.processDeleteResponse(result, config)
  }

  /**
   * 清除客户端缓存
   */
  clearCache(): void {
    this.clientCache.clear()
  }

  /**
   * 获取缓存的客户端数量
   */
  getCacheSize(): number {
    return this.clientCache.size
  }
}

// 默认 OData 适配器实例
let defaultODataAdapter: ODataAdapter | null = null

/**
 * 获取默认 OData 适配器实例
 */
export function getDefaultODataAdapter(): ODataAdapter {
  if (!defaultODataAdapter) {
    defaultODataAdapter = new ODataAdapter()
  }
  return defaultODataAdapter
}

/**
 * 设置默认 OData 适配器实例
 */
export function setDefaultODataAdapter(adapter: ODataAdapter): void {
  defaultODataAdapter = adapter
}

/**
 * 创建 OData 适配器实例
 */
export function createODataAdapter(): ODataAdapter {
  return new ODataAdapter()
}
