export const initialData = [
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
]
export const tableData = [
  {
    ncid: 'ncid1.plt0neuecadpart.local..e803e763-b234-4b2a-9749-2ec27d65c47c',
    createdBy: {
      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
      createdBy: {
        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
      },
      createdAt: '2025-04-11T09:24:58.626Z',
      schemaVersion: '',
      lifecycleState: 'ACTIVE',
      name: 'person3',
      email: '<EMAIL>',
      phone: '***********',
      icon: '',
      account: {
        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
      },
      person: {
        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
      },
    },
    createdAt: '2025-07-08T00:42:31.693Z',
    modifiedBy: {
      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
      createdBy: {
        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
      },
      createdAt: '2025-04-11T09:24:58.626Z',
      schemaVersion: '',
      lifecycleState: 'ACTIVE',
      name: 'person3',
      email: '<EMAIL>',
      phone: '***********',
      icon: '',
      account: {
        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
      },
      person: {
        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
      },
    },
    modifiedAt: '2025-07-08T00:45:08.483Z',
    schemaVersion: '1.0.0',
    partNo: 'P032161',
    partName: '第一层子装配A',
    partDescription: '这是一个子装update',
    mass: 93,
    material: 'est irure Duis non velit',
    gravityCenter: 'deserunt eu',
    volume: 85,
    solidSurfaceArea: 91,
    openSurfaceArea: 39,
    partType: 'NEUE_ASM',
    owner: {
      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
      createdBy: {
        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
      },
      createdAt: '2025-04-11T09:24:58.626Z',
      schemaVersion: '',
      lifecycleState: 'ACTIVE',
      name: 'person3',
      email: '<EMAIL>',
      phone: '***********',
      icon: '',
      account: {
        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
      },
      person: {
        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
      },
    },
    status: 'INWORK',
    lockState: 'UNLOCKED',
    lockedAt: '1970-01-01T00:00:00.000Z',
    version: '2',
    latestVersion: true,
    checkState: 'CHECKED_IN',
    checkedOutAt: '1970-01-01T00:00:00.000Z',
    master: {
      ncid: 'ncid1.plt0neuecadpartmaster.local..1232adb9-b759-481e-b784-0e9146ea7eec',
      createdAt: '2025-07-08T00:42:31.690Z',
      modifiedAt: '2025-07-08T00:42:31.690Z',
      schemaVersion: '1.0.0',
    },
    revision: 'A',
    latestRevision: true,
    cadboms: [
      {
        ncid: 'ncid1.plt0neuecadpart.local..051931a1-33e2-4901-a4cc-52c4d5956b87',
        createdAt: '2025-07-08T00:42:31.693Z',
        modifiedAt: '2025-07-08T00:45:08.483Z',
        schemaVersion: '1.0.0',
        source: {
          ncid: 'ncid1.plt0neuecadpart.local..e803e763-b234-4b2a-9749-2ec27d65c47c',
          createdAt: '2025-07-08T00:42:31.693Z',
          modifiedAt: '2025-07-08T00:45:08.483Z',
          schemaVersion: '1.0.0',
        },
        target: {
          ncid: 'ncid1.plt0neuecadpart.local..051931a1-33e2-4901-a4cc-52c4d5956b87',
          createdBy: {
            ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
            createdBy: {
              ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
            },
            createdAt: '2025-04-11T09:24:58.626Z',
            schemaVersion: '',
            lifecycleState: 'ACTIVE',
            name: 'person3',
            email: '<EMAIL>',
            phone: '***********',
            icon: '',
            account: {
              ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
            },
            person: {
              ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
            },
          },
          createdAt: '2025-07-08T00:42:31.693Z',
          modifiedBy: {
            ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
            createdBy: {
              ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
            },
            createdAt: '2025-04-11T09:24:58.626Z',
            schemaVersion: '',
            lifecycleState: 'ACTIVE',
            name: 'person3',
            email: '<EMAIL>',
            phone: '***********',
            icon: '',
            account: {
              ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
            },
            person: {
              ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
            },
          },
          modifiedAt: '2025-07-08T00:45:08.483Z',
          schemaVersion: '1.0.0',
          partNo: 'P032163',
          partName: '装配A的子件C',
          partDescription: '这是一个螺母2update',
          mass: 93,
          material: 'est irure Duis non velit',
          gravityCenter: 'deserunt eu',
          volume: 85,
          solidSurfaceArea: 91,
          openSurfaceArea: 39,
          partType: 'NEUE_PRT',
          owner: {
            ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
            createdBy: {
              ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
            },
            createdAt: '2025-04-11T09:24:58.626Z',
            schemaVersion: '',
            lifecycleState: 'ACTIVE',
            name: 'person3',
            email: '<EMAIL>',
            phone: '***********',
            icon: '',
            account: {
              ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
            },
            person: {
              ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
            },
          },
          status: 'INWORK',
          lockState: 'UNLOCKED',
          lockedAt: '1970-01-01T00:00:00.000Z',
          version: '2',
          latestVersion: true,
          checkState: 'CHECKED_IN',
          checkedOutAt: '1970-01-01T00:00:00.000Z',
          master: {
            ncid: 'ncid1.plt0neuecadpartmaster.local..296164f4-21ad-467c-956a-ed343eb69b2a',
            createdAt: '2025-07-08T00:42:31.690Z',
            modifiedAt: '2025-07-08T00:42:31.690Z',
            schemaVersion: '1.0.0',
          },
          revision: 'A',
          latestRevision: true,
          idi3DLightModelId:
            'ncid1.plt0ufrmeta.local..35f40fa9-8502-465d-84c0-6e440949b258',
          convertStatus: 'SUCCESS',
          cadboms: [],
        },
        instanceName: 'A_C_update',
        transformationMatrix: '1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1',
        quantity: 1,
        suppressed: false,
        configuration: 'CONFIG1',
        createdBy: {
          ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
          createdBy: {
            ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
          },
          createdAt: '2025-04-11T09:24:58.626Z',
          schemaVersion: '',
          lifecycleState: 'ACTIVE',
          name: 'person3',
          email: '<EMAIL>',
          phone: '***********',
          icon: '',
          account: {
            ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
          },
          person: {
            ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
          },
        },
        modifiedBy: {
          ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
          createdBy: {
            ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
          },
          createdAt: '2025-04-11T09:24:58.626Z',
          schemaVersion: '',
          lifecycleState: 'ACTIVE',
          name: 'person3',
          email: '<EMAIL>',
          phone: '***********',
          icon: '',
          account: {
            ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
          },
          person: {
            ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
          },
        },
        partNo: 'P032163',
        partName: '装配A的子件C',
        partDescription: '这是一个螺母2update',
        mass: 93,
        material: 'est irure Duis non velit',
        gravityCenter: 'deserunt eu',
        volume: 85,
        solidSurfaceArea: 91,
        openSurfaceArea: 39,
        partType: 'NEUE_PRT',
        owner: {
          ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
          createdBy: {
            ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
          },
          createdAt: '2025-04-11T09:24:58.626Z',
          schemaVersion: '',
          lifecycleState: 'ACTIVE',
          name: 'person3',
          email: '<EMAIL>',
          phone: '***********',
          icon: '',
          account: {
            ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
          },
          person: {
            ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
          },
        },
        status: 'INWORK',
        lockState: 'UNLOCKED',
        lockedAt: '1970-01-01T00:00:00.000Z',
        version: '2',
        latestVersion: true,
        checkState: 'CHECKED_IN',
        checkedOutAt: '1970-01-01T00:00:00.000Z',
        master: {
          ncid: 'ncid1.plt0neuecadpartmaster.local..296164f4-21ad-467c-956a-ed343eb69b2a',
          createdAt: '2025-07-08T00:42:31.690Z',
          modifiedAt: '2025-07-08T00:42:31.690Z',
          schemaVersion: '1.0.0',
        },
        revision: 'A',
        latestRevision: true,
        idi3DLightModelId:
          'ncid1.plt0ufrmeta.local..35f40fa9-8502-465d-84c0-6e440949b258',
        convertStatus: 'SUCCESS',
        cadboms: [],
        idPath: '0-0-0',
      },
      {
        ncid: 'ncid1.plt0neuecadpart.local..f307be0b-5a79-4382-9695-28150c813d53',
        createdAt: '2025-07-08T00:42:31.693Z',
        modifiedAt: '2025-07-08T00:45:08.483Z',
        schemaVersion: '1.0.0',
        source: {
          ncid: 'ncid1.plt0neuecadpart.local..e803e763-b234-4b2a-9749-2ec27d65c47c',
          createdAt: '2025-07-08T00:42:31.693Z',
          modifiedAt: '2025-07-08T00:45:08.483Z',
          schemaVersion: '1.0.0',
        },
        target: {
          ncid: 'ncid1.plt0neuecadpart.local..f307be0b-5a79-4382-9695-28150c813d53',
          createdBy: {
            ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
            createdBy: {
              ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
            },
            createdAt: '2025-04-11T09:24:58.626Z',
            schemaVersion: '',
            lifecycleState: 'ACTIVE',
            name: 'person3',
            email: '<EMAIL>',
            phone: '***********',
            icon: '',
            account: {
              ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
            },
            person: {
              ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
            },
          },
          createdAt: '2025-07-08T00:42:31.693Z',
          modifiedBy: {
            ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
            createdBy: {
              ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
            },
            createdAt: '2025-04-11T09:24:58.626Z',
            schemaVersion: '',
            lifecycleState: 'ACTIVE',
            name: 'person3',
            email: '<EMAIL>',
            phone: '***********',
            icon: '',
            account: {
              ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
            },
            person: {
              ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
            },
          },
          modifiedAt: '2025-07-08T00:45:08.483Z',
          schemaVersion: '1.0.0',
          partNo: 'P032162',
          partName: '装配A的子件B',
          partDescription: '这是一个螺栓1update',
          mass: 93,
          material: 'est irure Duis non velit',
          gravityCenter: 'deserunt eu',
          volume: 85,
          solidSurfaceArea: 91,
          openSurfaceArea: 39,
          partType: 'NEUE_PRT',
          owner: {
            ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
            createdBy: {
              ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
            },
            createdAt: '2025-04-11T09:24:58.626Z',
            schemaVersion: '',
            lifecycleState: 'ACTIVE',
            name: 'person3',
            email: '<EMAIL>',
            phone: '***********',
            icon: '',
            account: {
              ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
            },
            person: {
              ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
            },
          },
          status: 'INWORK',
          lockState: 'UNLOCKED',
          lockedAt: '1970-01-01T00:00:00.000Z',
          version: '2',
          latestVersion: true,
          checkState: 'CHECKED_IN',
          checkedOutAt: '1970-01-01T00:00:00.000Z',
          master: {
            ncid: 'ncid1.plt0neuecadpartmaster.local..87c0007f-7e18-491c-aa05-675b4263e68c',
            createdAt: '2025-07-08T00:42:31.690Z',
            modifiedAt: '2025-07-08T00:42:31.690Z',
            schemaVersion: '1.0.0',
          },
          revision: 'A',
          latestRevision: true,
          idi3DLightModelId:
            'ncid1.plt0ufrmeta.local..bea160ce-8234-402c-b5cb-9c98a1195bad',
          convertStatus: 'SUCCESS',
          cadboms: [],
        },
        instanceName: 'A_B_update',
        transformationMatrix: '1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1',
        quantity: 1,
        suppressed: false,
        configuration: 'CONFIG1',
        createdBy: {
          ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
          createdBy: {
            ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
          },
          createdAt: '2025-04-11T09:24:58.626Z',
          schemaVersion: '',
          lifecycleState: 'ACTIVE',
          name: 'person3',
          email: '<EMAIL>',
          phone: '***********',
          icon: '',
          account: {
            ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
          },
          person: {
            ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
          },
        },
        modifiedBy: {
          ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
          createdBy: {
            ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
          },
          createdAt: '2025-04-11T09:24:58.626Z',
          schemaVersion: '',
          lifecycleState: 'ACTIVE',
          name: 'person3',
          email: '<EMAIL>',
          phone: '***********',
          icon: '',
          account: {
            ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
          },
          person: {
            ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
          },
        },
        partNo: 'P032162',
        partName: '装配A的子件B',
        partDescription: '这是一个螺栓1update',
        mass: 93,
        material: 'est irure Duis non velit',
        gravityCenter: 'deserunt eu',
        volume: 85,
        solidSurfaceArea: 91,
        openSurfaceArea: 39,
        partType: 'NEUE_PRT',
        owner: {
          ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
          createdBy: {
            ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
          },
          createdAt: '2025-04-11T09:24:58.626Z',
          schemaVersion: '',
          lifecycleState: 'ACTIVE',
          name: 'person3',
          email: '<EMAIL>',
          phone: '***********',
          icon: '',
          account: {
            ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
          },
          person: {
            ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
          },
        },
        status: 'INWORK',
        lockState: 'UNLOCKED',
        lockedAt: '1970-01-01T00:00:00.000Z',
        version: '2',
        latestVersion: true,
        checkState: 'CHECKED_IN',
        checkedOutAt: '1970-01-01T00:00:00.000Z',
        master: {
          ncid: 'ncid1.plt0neuecadpartmaster.local..87c0007f-7e18-491c-aa05-675b4263e68c',
          createdAt: '2025-07-08T00:42:31.690Z',
          modifiedAt: '2025-07-08T00:42:31.690Z',
          schemaVersion: '1.0.0',
        },
        revision: 'A',
        latestRevision: true,
        idi3DLightModelId:
          'ncid1.plt0ufrmeta.local..bea160ce-8234-402c-b5cb-9c98a1195bad',
        convertStatus: 'SUCCESS',
        cadboms: [],
        idPath: '0-0-1',
      },
    ],
    idPath: '0',
  },
]

export const pageData = {
  config: {
    configProvider: {
      // locale: "locale === 'en' ? enUS : zhCN",
      // size: 'large',
    },
    // 页面的组件属性配置
    // events: [],
    // api: {},
  },
  events: {
    onClick: {
      name: 'onBeforeMount',
      params: ['e'],
      body: `console.log('clicked', e);\nalert('hi');`,
    },
    onChange: {
      name: 'onMounted',
      params: ['value'],
      body: `console.log('value changed', value);`,
    },
  },
  apis: {
    userList07d5b003: {
      url: '/user/list',
      method: 'get',
      params: {
        deptId: '{{form.deptId}}',
      },
      map: { label: 'name', value: 'id' },
    },
    userInfo07d5b003: {
      url: '/user/list',
      method: 'post',
      body: {
        deptId: '{{form.deptId}}',
      },
      map: { label: 'name', value: 'id' },
    },
  },
  elements: [
    {
      id: 'bom-table_60spo02i5g', // 组件唯一身份
      type: 'widget-container',
      name: '搜索表单',
      props: {},
      events: [],
      elements: [
        {
          id: 'table_60spo02i5g', // 组件唯一身份
          type: 'bom-table',
          name: '搜索表单',
          props: {
            columns: [
              { prop: 'partNo', label: '零部件编号' },
              {
                prop: 'partName',
                label: '零部件名称',
                showOverflowTooltip: true,
              },
              {
                prop: 'instanceName',
                label: '实例名称',
                showOverflowTooltip: true,
              },
              { prop: 'version', label: '版本号' },
              {
                label: '状态',
                prop: 'status',
                valueEnum: {
                  working: { text: '工作中', status: 'success' },
                },
              },
              {
                label: '修改人',
                prop: 'partType',
                valueType: 'avatar',
              },
              {
                label: '修改时间',
                prop: 'modifiedAt',
                valueType: 'dateTime',
                timeFormat: 'YYYY-MM-DD',
                sortable: true,
              },
            ],
            data: tableData,
            request: {
              url: '/api/bom/list',
              method: 'get',
              params: {
                deptId: '{{form.deptId}}',
              },
            },
          },
          events: [
            {
              nickName: '行点击事件',
              eventName: 'onRowClick',
              actions: [
                {
                  id: 'start',
                  type: 'start',
                  title: '开始',
                },
                {
                  id: '56132221',
                  type: 'normal',
                  title: '节点7690',
                  content: '打开弹框',
                  config: {
                    actionType: 'open',
                    actionName: '打开drawer',
                    target: 'Drawer_er4mn6jbtk',
                  },
                  children: [],
                },
                {
                  id: 'end',
                  type: 'end',
                  title: '结束',
                },
              ],
            },
          ],
          elements: [],
        },
        {
          id: 'widget-table_60spo02i5g', // 组件唯一身份
          type: 'widget-table',
          name: '搜索表单',
          props: {
            columns: [
              { prop: 'partNo', label: '零部件编号' },
              {
                prop: 'partName',
                label: '零部件名称',
                showOverflowTooltip: true,
              },
              {
                prop: 'instanceName',
                label: '实例名称',
                showOverflowTooltip: true,
              },
              { prop: 'version', label: '版本号' },
              {
                label: '状态',
                prop: 'status',
                valueEnum: {
                  working: { text: '工作中', status: 'success' },
                },
              },
              {
                label: '修改人',
                prop: 'partType',
                valueType: 'avatar',
              },
              {
                label: '修改时间',
                prop: 'modifiedAt',
                valueType: 'dateTime',
                timeFormat: 'YYYY-MM-DD',
                sortable: true,
              },
            ],
            request: {
              url: '/api/bom/list',
              method: 'get',
              params: {
                deptId: '{{form.deptId}}',
              },
            },
          },
          events: [],
          elements: [],
        },
      ],
    },
    {
      id: 'Drawer_er4mn6jbtk',
      type: 'drawer',
      name: '弹框',
      props: {
        modelValue: false,
        size: '1061px',
      },
      elements: [
        {
          id: 'Tabs_3gx13gh2ht',
          type: 'tabs',
          name: 'tabs',
          props: {},
          elements: [
            {
              id: 'TabPane_3gx13gh2ht',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '属性信息',
              },
              elements: [],
            },
            {
              id: 'TabPane_usage',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '被用于',
              },
              elements: [],
            },
            {
              id: 'TabPane_history',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '历史记录',
              },
              elements: [],
            },
            {
              id: 'TabPane_useBy',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '被用于',
              },
              elements: [],
            },
          ],
        },
      ],
      events: [
        {
          nickName: '弹框关闭',
          eventName: 'onClosed',
          actions: [
            {
              id: 'start',
              type: 'start',
              title: '开始',
            },
            {
              id: '561322123',
              type: 'normal',
              title: '节点561322123',
              content: '弹框关闭回调',
              config: {
                actionType: 'refresh',
                actionName: '刷新表格',
                target: 'table_60spo02i5g',
              },
            },
            {
              id: 'end',
              type: 'end',
              title: '结束',
            },
          ],
        },
      ],
      slots: {
        header: [
          {
            id: 'button_fb19o54fjh',
            type: 'div',
            name: '文本框',
            props: {
              style: 'font-size:30px;font-weight:600;color:#0F172A',
              innerHTML: 'UPX10007248',
            },
          },
        ],
        footer: 'footer',
      },
    },
  ],
}
export const odataMaterialConfig = {
  elements: [
    {
      id: 'odata-card',
      type: 'card',
      name: 'OData表格',
      elements: [
        {
          id: 'odata-table',
          type: 'pro-table',
          name: 'OData表格',
          props: {
            columns: [
              { prop: 'FirstName', label: '名' },
              { prop: 'LastName', label: '姓' },
              { prop: 'Gender', label: '性别' },
            ],
          },
          apis: {
            request: {
              url: 'https://services.odata.org/V4/TripPinServiceRW/Products',
              method: 'get',
              protocol: 'odata',
              params: {
                select: 'FirstName,LastName,Gender',
                top: 5,
              },
              dataPath: 'value',
            },
          },
        },
      ],
    },
  ],
  apis: {
    request: {
      url: 'https://services.odata.org/V4/TripPinServiceRW/People',
      method: 'get',
      protocol: 'odata',
      odata: {
        select: 'FirstName,LastName,Gender',
        top: 5,
      },
      dataPath: 'value',
    },
    apiJson: {
      sourceType: 'json',
      source: [],
    },
  },
}

export const bomTableConfig = {
  config: {
    configProvider: {
      // locale: "locale === 'en' ? enUS : zhCN",
      // size: 'large',
    },
    // 页面的组件属性配置
    // events: [],
    // api: {},
  },
  events: {
    onClick: {
      name: 'onBeforeMount',
      params: ['e'],
      body: `console.log('clicked', e);\nalert('hi');`,
    },
    onChange: {
      name: 'onMounted',
      params: ['value'],
      body: `console.log('value changed', value);`,
    },
  },
  apis: {
    api: {
      sourceType: 'json',
      source: [],
    },
  },
  elements: [
    {
      id: 'bom-table_60spo02i5g', // 组件唯一身份
      type: 'widget-container',
      name: '搜索表单',
      props: {},
      events: [],
      elements: [
        {
          id: 'bom-table_60spo02i5g', // 组件唯一身份
          type: 'wg-bom-table',
          name: '搜索表单',
          props: {
            columns: [
              { prop: 'partNo', label: '零部件编号', width: '360px' },
              {
                prop: 'partName',
                label: '零部件名称',
                showOverflowTooltip: true,
              },
              {
                prop: 'instanceName',
                label: '实例名称',
                showOverflowTooltip: true,
              },
              { prop: 'version', label: '版本号' },
              {
                label: '状态',
                prop: 'status',
                valueEnum: {
                  working: { text: '工作中', status: 'success' },
                  INWORK: { text: '工作中', status: 'success' },
                },
              },
              {
                label: '修改人',
                prop: 'partType',
                valueType: 'avatar',
              },
              {
                label: '修改时间',
                prop: 'modifiedAt',
                valueType: 'dateTime',
                timeFormat: 'YYYY-MM-DD',
                sortable: true,
              },
            ],
            data: [
              {
                ncid: 'ncid1.plt0neuecadpart.local..e803e763-b234-4b2a-9749-2ec27d65c47c',
                createdBy: {
                  ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                  createdBy: {
                    ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                  },
                  createdAt: '2025-04-11T09:24:58.626Z',
                  schemaVersion: '',
                  lifecycleState: 'ACTIVE',
                  name: 'person3',
                  email: '<EMAIL>',
                  phone: '***********',
                  icon: '',
                  account: {
                    ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                  },
                  person: {
                    ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                  },
                },
                createdAt: '2025-07-08T00:42:31.693Z',
                modifiedBy: {
                  ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                  createdBy: {
                    ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                  },
                  createdAt: '2025-04-11T09:24:58.626Z',
                  schemaVersion: '',
                  lifecycleState: 'ACTIVE',
                  name: 'person3',
                  email: '<EMAIL>',
                  phone: '***********',
                  icon: '',
                  account: {
                    ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                  },
                  person: {
                    ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                  },
                },
                modifiedAt: '2025-07-08T00:45:08.483Z',
                schemaVersion: '1.0.0',
                partNo: 'P032161',
                partName: '第一层子装配A111111',
                partDescription: '这是一个子装update',
                mass: 93,
                material: 'est irure Duis non velit',
                gravityCenter: 'deserunt eu',
                volume: 85,
                solidSurfaceArea: 91,
                openSurfaceArea: 39,
                partType: 'NEUE_ASM',
                owner: {
                  ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                  createdBy: {
                    ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                  },
                  createdAt: '2025-04-11T09:24:58.626Z',
                  schemaVersion: '',
                  lifecycleState: 'ACTIVE',
                  name: 'person3',
                  email: '<EMAIL>',
                  phone: '***********',
                  icon: '',
                  account: {
                    ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                  },
                  person: {
                    ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                  },
                },
                status: 'INWORK',
                lockState: 'UNLOCKED',
                lockedAt: '1970-01-01T00:00:00.000Z',
                version: '2',
                latestVersion: true,
                checkState: 'CHECKED_IN',
                checkedOutAt: '1970-01-01T00:00:00.000Z',
                master: {
                  ncid: 'ncid1.plt0neuecadpartmaster.local..1232adb9-b759-481e-b784-0e9146ea7eec',
                  createdAt: '2025-07-08T00:42:31.690Z',
                  modifiedAt: '2025-07-08T00:42:31.690Z',
                  schemaVersion: '1.0.0',
                },
                revision: 'A',
                latestRevision: true,
                cadboms: [
                  {
                    ncid: 'ncid1.plt0neuecadpart.local..051931a1-33e2-4901-a4cc-52c4d5956b87',
                    createdAt: '2025-07-08T00:42:31.693Z',
                    modifiedAt: '2025-07-08T00:45:08.483Z',
                    schemaVersion: '1.0.0',
                    source: {
                      ncid: 'ncid1.plt0neuecadpart.local..e803e763-b234-4b2a-9749-2ec27d65c47c',
                      createdAt: '2025-07-08T00:42:31.693Z',
                      modifiedAt: '2025-07-08T00:45:08.483Z',
                      schemaVersion: '1.0.0',
                    },
                    target: {
                      ncid: 'ncid1.plt0neuecadpart.local..051931a1-33e2-4901-a4cc-52c4d5956b87',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                        createdBy: {
                          ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                        },
                        createdAt: '2025-04-11T09:24:58.626Z',
                        schemaVersion: '',
                        lifecycleState: 'ACTIVE',
                        name: 'person3',
                        email: '<EMAIL>',
                        phone: '***********',
                        icon: '',
                        account: {
                          ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                        },
                        person: {
                          ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                        },
                      },
                      createdAt: '2025-07-08T00:42:31.693Z',
                      modifiedBy: {
                        ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                        createdBy: {
                          ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                        },
                        createdAt: '2025-04-11T09:24:58.626Z',
                        schemaVersion: '',
                        lifecycleState: 'ACTIVE',
                        name: 'person3',
                        email: '<EMAIL>',
                        phone: '***********',
                        icon: '',
                        account: {
                          ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                        },
                        person: {
                          ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                        },
                      },
                      modifiedAt: '2025-07-08T00:45:08.483Z',
                      schemaVersion: '1.0.0',
                      partNo: 'P032163',
                      partName: '装配A的子件C',
                      partDescription: '这是一个螺母2update',
                      mass: 93,
                      material: 'est irure Duis non velit',
                      gravityCenter: 'deserunt eu',
                      volume: 85,
                      solidSurfaceArea: 91,
                      openSurfaceArea: 39,
                      partType: 'NEUE_PRT',
                      owner: {
                        ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                        createdBy: {
                          ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                        },
                        createdAt: '2025-04-11T09:24:58.626Z',
                        schemaVersion: '',
                        lifecycleState: 'ACTIVE',
                        name: 'person3',
                        email: '<EMAIL>',
                        phone: '***********',
                        icon: '',
                        account: {
                          ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                        },
                        person: {
                          ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                        },
                      },
                      status: 'INWORK',
                      lockState: 'UNLOCKED',
                      lockedAt: '1970-01-01T00:00:00.000Z',
                      version: '2',
                      latestVersion: true,
                      checkState: 'CHECKED_IN',
                      checkedOutAt: '1970-01-01T00:00:00.000Z',
                      master: {
                        ncid: 'ncid1.plt0neuecadpartmaster.local..296164f4-21ad-467c-956a-ed343eb69b2a',
                        createdAt: '2025-07-08T00:42:31.690Z',
                        modifiedAt: '2025-07-08T00:42:31.690Z',
                        schemaVersion: '1.0.0',
                      },
                      revision: 'A',
                      latestRevision: true,
                      idi3DLightModelId:
                        'ncid1.plt0ufrmeta.local..35f40fa9-8502-465d-84c0-6e440949b258',
                      convertStatus: 'SUCCESS',
                      cadboms: [],
                    },
                    instanceName: 'A_C_update',
                    transformationMatrix: '1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1',
                    quantity: 1,
                    suppressed: false,
                    configuration: 'CONFIG1',
                    createdBy: {
                      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                      },
                      createdAt: '2025-04-11T09:24:58.626Z',
                      schemaVersion: '',
                      lifecycleState: 'ACTIVE',
                      name: 'person3',
                      email: '<EMAIL>',
                      phone: '***********',
                      icon: '',
                      account: {
                        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                      },
                      person: {
                        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                      },
                    },
                    modifiedBy: {
                      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                      },
                      createdAt: '2025-04-11T09:24:58.626Z',
                      schemaVersion: '',
                      lifecycleState: 'ACTIVE',
                      name: 'person3',
                      email: '<EMAIL>',
                      phone: '***********',
                      icon: '',
                      account: {
                        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                      },
                      person: {
                        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                      },
                    },
                    partNo: 'P032163',
                    partName: '装配A的子件C',
                    partDescription: '这是一个螺母2update',
                    mass: 93,
                    material: 'est irure Duis non velit',
                    gravityCenter: 'deserunt eu',
                    volume: 85,
                    solidSurfaceArea: 91,
                    openSurfaceArea: 39,
                    partType: 'NEUE_PRT',
                    owner: {
                      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                      },
                      createdAt: '2025-04-11T09:24:58.626Z',
                      schemaVersion: '',
                      lifecycleState: 'ACTIVE',
                      name: 'person3',
                      email: '<EMAIL>',
                      phone: '***********',
                      icon: '',
                      account: {
                        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                      },
                      person: {
                        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                      },
                    },
                    status: 'INWORK',
                    lockState: 'UNLOCKED',
                    lockedAt: '1970-01-01T00:00:00.000Z',
                    version: '2',
                    latestVersion: true,
                    checkState: 'CHECKED_IN',
                    checkedOutAt: '1970-01-01T00:00:00.000Z',
                    master: {
                      ncid: 'ncid1.plt0neuecadpartmaster.local..296164f4-21ad-467c-956a-ed343eb69b2a',
                      createdAt: '2025-07-08T00:42:31.690Z',
                      modifiedAt: '2025-07-08T00:42:31.690Z',
                      schemaVersion: '1.0.0',
                    },
                    revision: 'A',
                    latestRevision: true,
                    idi3DLightModelId:
                      'ncid1.plt0ufrmeta.local..35f40fa9-8502-465d-84c0-6e440949b258',
                    convertStatus: 'SUCCESS',
                    cadboms: [],
                    idPath: '0-0-0',
                  },
                  {
                    ncid: 'ncid1.plt0neuecadpart.local..f307be0b-5a79-4382-9695-28150c813d53',
                    createdAt: '2025-07-08T00:42:31.693Z',
                    modifiedAt: '2025-07-08T00:45:08.483Z',
                    schemaVersion: '1.0.0',
                    source: {
                      ncid: 'ncid1.plt0neuecadpart.local..e803e763-b234-4b2a-9749-2ec27d65c47c',
                      createdAt: '2025-07-08T00:42:31.693Z',
                      modifiedAt: '2025-07-08T00:45:08.483Z',
                      schemaVersion: '1.0.0',
                    },
                    target: {
                      ncid: 'ncid1.plt0neuecadpart.local..f307be0b-5a79-4382-9695-28150c813d53',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                        createdBy: {
                          ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                        },
                        createdAt: '2025-04-11T09:24:58.626Z',
                        schemaVersion: '',
                        lifecycleState: 'ACTIVE',
                        name: 'person3',
                        email: '<EMAIL>',
                        phone: '***********',
                        icon: '',
                        account: {
                          ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                        },
                        person: {
                          ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                        },
                      },
                      createdAt: '2025-07-08T00:42:31.693Z',
                      modifiedBy: {
                        ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                        createdBy: {
                          ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                        },
                        createdAt: '2025-04-11T09:24:58.626Z',
                        schemaVersion: '',
                        lifecycleState: 'ACTIVE',
                        name: 'person3',
                        email: '<EMAIL>',
                        phone: '***********',
                        icon: '',
                        account: {
                          ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                        },
                        person: {
                          ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                        },
                      },
                      modifiedAt: '2025-07-08T00:45:08.483Z',
                      schemaVersion: '1.0.0',
                      partNo: 'P032162',
                      partName: '装配A的子件B',
                      partDescription: '这是一个螺栓1update',
                      mass: 93,
                      material: 'est irure Duis non velit',
                      gravityCenter: 'deserunt eu',
                      volume: 85,
                      solidSurfaceArea: 91,
                      openSurfaceArea: 39,
                      partType: 'NEUE_PRT',
                      owner: {
                        ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                        createdBy: {
                          ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                        },
                        createdAt: '2025-04-11T09:24:58.626Z',
                        schemaVersion: '',
                        lifecycleState: 'ACTIVE',
                        name: 'person3',
                        email: '<EMAIL>',
                        phone: '***********',
                        icon: '',
                        account: {
                          ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                        },
                        person: {
                          ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                        },
                      },
                      status: 'INWORK',
                      lockState: 'UNLOCKED',
                      lockedAt: '1970-01-01T00:00:00.000Z',
                      version: '2',
                      latestVersion: true,
                      checkState: 'CHECKED_IN',
                      checkedOutAt: '1970-01-01T00:00:00.000Z',
                      master: {
                        ncid: 'ncid1.plt0neuecadpartmaster.local..87c0007f-7e18-491c-aa05-675b4263e68c',
                        createdAt: '2025-07-08T00:42:31.690Z',
                        modifiedAt: '2025-07-08T00:42:31.690Z',
                        schemaVersion: '1.0.0',
                      },
                      revision: 'A',
                      latestRevision: true,
                      idi3DLightModelId:
                        'ncid1.plt0ufrmeta.local..bea160ce-8234-402c-b5cb-9c98a1195bad',
                      convertStatus: 'SUCCESS',
                      cadboms: [],
                    },
                    instanceName: 'A_B_update',
                    transformationMatrix: '1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1',
                    quantity: 1,
                    suppressed: false,
                    configuration: 'CONFIG1',
                    createdBy: {
                      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                      },
                      createdAt: '2025-04-11T09:24:58.626Z',
                      schemaVersion: '',
                      lifecycleState: 'ACTIVE',
                      name: 'person3',
                      email: '<EMAIL>',
                      phone: '***********',
                      icon: '',
                      account: {
                        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                      },
                      person: {
                        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                      },
                    },
                    modifiedBy: {
                      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                      },
                      createdAt: '2025-04-11T09:24:58.626Z',
                      schemaVersion: '',
                      lifecycleState: 'ACTIVE',
                      name: 'person3',
                      email: '<EMAIL>',
                      phone: '***********',
                      icon: '',
                      account: {
                        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                      },
                      person: {
                        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                      },
                    },
                    partNo: 'P032162',
                    partName: '装配A的子件B',
                    partDescription: '这是一个螺栓1update',
                    mass: 93,
                    material: 'est irure Duis non velit',
                    gravityCenter: 'deserunt eu',
                    volume: 85,
                    solidSurfaceArea: 91,
                    openSurfaceArea: 39,
                    partType: 'NEUE_PRT',
                    owner: {
                      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                      },
                      createdAt: '2025-04-11T09:24:58.626Z',
                      schemaVersion: '',
                      lifecycleState: 'ACTIVE',
                      name: 'person3',
                      email: '<EMAIL>',
                      phone: '***********',
                      icon: '',
                      account: {
                        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                      },
                      person: {
                        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                      },
                    },
                    status: 'INWORK',
                    lockState: 'UNLOCKED',
                    lockedAt: '1970-01-01T00:00:00.000Z',
                    version: '2',
                    latestVersion: true,
                    checkState: 'CHECKED_IN',
                    checkedOutAt: '1970-01-01T00:00:00.000Z',
                    master: {
                      ncid: 'ncid1.plt0neuecadpartmaster.local..87c0007f-7e18-491c-aa05-675b4263e68c',
                      createdAt: '2025-07-08T00:42:31.690Z',
                      modifiedAt: '2025-07-08T00:42:31.690Z',
                      schemaVersion: '1.0.0',
                    },
                    revision: 'A',
                    latestRevision: true,
                    idi3DLightModelId:
                      'ncid1.plt0ufrmeta.local..bea160ce-8234-402c-b5cb-9c98a1195bad',
                    convertStatus: 'SUCCESS',
                    cadboms: [],
                    idPath: '0-0-1',
                  },
                ],
                idPath: '0',
              },
            ],
          },
          events: [
            {
              nickName: '行点击事件',
              eventName: 'onRowClick',
              actions: [
                {
                  id: 'start',
                  type: 'start',
                  title: '开始',
                },
                {
                  id: '56132221',
                  type: 'normal',
                  title: '节点7690',
                  content: '打开弹框',
                  config: {
                    actionType: 'open',
                    actionName: '打开drawer',
                    target: 'bom-panel_60spo02i5g',
                  },
                  children: [],
                },
                {
                  id: 'end',
                  type: 'end',
                  title: '结束',
                },
              ],
            },
          ],
          elements: [],
        },
        {
          id: 'bom-panel_60spo02i5g', // 组件唯一身份
          type: 'bom-panel',
          name: '搜索表单',
          props: {},
          events: [],
          elements: [],
        },
      ],
    },
  ],
}

export const basicFormConfig = {
  config: {
    configProvider: {
      // locale: "locale === 'en' ? enUS : zhCN",
      // size: 'large',
    },
    // 页面的组件属性配置
    // events: [],
    // api: {},
  },
  events: {},
  apis: {
    api: {
      sourceType: 'json',
      source: [],
    },
  },
  elements: [
    {
      id: 'basic-form_60spo02i6g', // 组件唯一身份
      type: 'widget-container',
      name: '基础表单',
      props: {},
      events: [],
      elements: [
        {
          id: 'basic-form_60spo02i5g', // 组件唯一身份
          type: 'wg-basic-form',
          name: '基础表单',
          props: {
            formItems: [
              {
                prop: 'ncid',
                fieldName: '编码',
                fieldType: 'text',
                required: true,
                ruleId: 'RULE_USERNAME',
              },
              {
                prop: 'name',
                fieldName: '名称1',
                fieldType: 'text',
                required: true,
                minLength: 6,
                maxLength: 18,
                showWordLimit: true,
                ruleId: 'RULE_USERNAME',
              },
              {
                prop: 'version',
                fieldName: '版本',
                fieldType: 'text',
                disabled: true,
                required: true,
                ruleId: 'RULE_EMAIL',
              },
              {
                prop: 'version1',
                fieldName: '版次',
                fieldType: 'text',
                disabled: true,
                required: true,
                ruleId: 'RULE_EMAIL',
              },
              {
                prop: 'age',
                fieldName: '年龄',
                fieldType: 'number',
                ruleId: 'RULE_AGE',
              },
              {
                prop: 'city',
                fieldName: '城市',
                fieldType: 'select',
                filterable: true,
                options: [
                  { label: '北京', value: 'beijing' },
                  { label: '上海', value: 'shanghai' },
                  { label: '广州', value: 'guangzhou' },
                ],
              },
              {
                prop: 'gender',
                fieldName: '性别',
                fieldType: 'radio',
                options: [
                  { label: '男', value: 'male' },
                  { label: '女', value: 'female' },
                ],
              },
              {
                prop: 'hobbies',
                fieldName: '爱好',
                fieldType: 'checkbox',
                options: [
                  { label: '阅读', value: 'reading' },
                  { label: '运动', value: 'sports' },
                ],
              },
              {
                prop: 'birthday',
                fieldName: '生日',
                fieldType: 'date',
                dateFormat: 'YYYY-MM-DD',
              },
              {
                prop: 'agreement',
                fieldName: '同意协议',
                fieldType: 'switch',
              },
              {
                prop: 'remark',
                fieldName: '备注',
                fieldType: 'textarea',
                showWordLimit: true,
                maxLength: 200,
              },
            ],
            data: [],
          },
          events: [
            {
              nickName: '行点击事件',
              eventName: 'onRowClick',
              actions: [
                {
                  id: 'start',
                  type: 'start',
                  title: '开始',
                },
                {
                  id: '56132221',
                  type: 'normal',
                  title: '节点7690',
                  content: '打开弹框',
                  config: {
                    actionType: 'open',
                    actionName: '打开drawer',
                    target: 'bom-panel_60spo02i5g',
                  },
                  children: [],
                },
                {
                  id: 'end',
                  type: 'end',
                  title: '结束',
                },
              ],
            },
          ],
          elements: [],
        },
      ],
    },
  ],
}
