import { keys } from 'lodash-unified'
import { ElDatePicker, ElInputNumber } from 'element-plus'
import { ValueTypeEnum } from '@neue-plus/components/pro-table'
import NeSelect from '../../select'
import NeCheckboxGroup from '../../checkbox-group'
import NeDateRange from '../../date-range'
import NeInput from '../../input'
import { ProFormItemProps } from './types'

export function useComponentMap() {
  const getComponentMap = (type?: ValueTypeEnum) => {
    let component: any = NeInput
    switch (type) {
      case 'text':
      case 'input':
        component = NeInput
        break
      case 'select':
        component = NeSelect
        break
      case 'checkbox':
        component = NeCheckboxGroup
        break
      case 'dateRange':
        component = NeDateRange
        break
      case 'date':
        component = ElDatePicker
        break
      case 'switch':
        component = NeInput
        break
      case 'digit':
        component = ElInputNumber
        break
      default:
        component = NeInput
        break
    }
    return component
  }

  // 获取默认值
  const getDefaultValue = (type?: ValueTypeEnum) => {
    switch (type) {
      case 'radio':
      case 'checkbox':
      case 'dateRange':
        return []
      case 'switch':
        return false
      case 'digit':
        return 0
      default:
        return ''
    }
  }
  const getFormItemProps = (formItem: ProFormItemProps) => {
    const { valueType, placeholder, valueEnum = {} } = formItem
    let props: any = { placeholder }
    switch (valueType) {
      case 'text':
      case 'input':
        props = { placeholder }
        break
      case 'select':
        props = {
          placeholder,
          options: keys(valueEnum).map((key) => ({
            label: valueEnum[key].text,
            value: key,
          })),
        }
        break
      case 'checkbox':
        props = { placeholder }
        break
      case 'dateRange':
      case 'date':
        props = { placeholder }
        break
      case 'switch':
        props = { placeholder }
        break
      case 'digit':
        props = { placeholder }
        break
      default:
        props = { placeholder }
        break
    }
    return props
  }
  return {
    getComponentMap,
    getDefaultValue,
    getFormItemProps,
  }
}
