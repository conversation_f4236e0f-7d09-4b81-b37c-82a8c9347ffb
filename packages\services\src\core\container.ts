/**
 * 服务容器实现
 * 提供依赖注入和服务管理功能
 */

import type { ServiceContainer } from './interfaces'

/**
 * 服务定义
 */
interface ServiceDefinition<T = any> {
  factory: () => T
  singleton: boolean
  instance?: T
}

/**
 * 默认服务容器实现
 */
export class DefaultServiceContainer implements ServiceContainer {
  private services = new Map<string, ServiceDefinition>()

  /**
   * 注册服务
   */
  register<T = any>(
    name: string,
    factory: () => T,
    singleton: boolean = true
  ): void {
    this.services.set(name, {
      factory,
      singleton,
    })
  }

  /**
   * 获取服务
   */
  get<T = any>(name: string): T {
    const definition = this.services.get(name)
    if (!definition) {
      throw new Error(`Service '${name}' not found`)
    }

    if (definition.singleton) {
      if (!definition.instance) {
        definition.instance = definition.factory()
      }
      return definition.instance as T
    }

    return definition.factory() as T
  }

  /**
   * 检查服务是否存在
   */
  has(name: string): boolean {
    return this.services.has(name)
  }

  /**
   * 清除所有服务
   */
  clear(): void {
    this.services.clear()
  }

  /**
   * 获取所有已注册的服务名称
   */
  getServiceNames(): string[] {
    return Array.from(this.services.keys())
  }
}

// 全局服务容器实例
let globalContainer: ServiceContainer | null = null

/**
 * 获取全局服务容器
 */
export function getGlobalContainer(): ServiceContainer {
  if (!globalContainer) {
    globalContainer = new DefaultServiceContainer()
  }
  return globalContainer
}

/**
 * 设置全局服务容器
 */
export function setGlobalContainer(container: ServiceContainer): void {
  globalContainer = container
}

/**
 * 创建新的服务容器
 */
export function createContainer(): ServiceContainer {
  return new DefaultServiceContainer()
}

/**
 * 服务装饰器
 * 用于自动注册服务
 */
export function Service(name: string, singleton: boolean = true) {
  return function <T extends new (...args: any[]) => any>(constructor: T) {
    const container = getGlobalContainer()
    container.register(name, () => new constructor(), singleton)
    return constructor
  }
}

/**
 * 注入装饰器
 * 用于自动注入依赖
 */
export function Inject(serviceName: string) {
  return function (target: any, propertyKey: string) {
    Object.defineProperty(target, propertyKey, {
      get() {
        const container = getGlobalContainer()
        return container.get(serviceName)
      },
      enumerable: true,
      configurable: true,
    })
  }
}

/**
 * 便捷的服务注册函数
 */
export function registerService<T>(
  name: string,
  factory: () => T,
  singleton: boolean = true
): void {
  const container = getGlobalContainer()
  container.register(name, factory, singleton)
}

/**
 * 便捷的服务获取函数
 */
export function getService<T = any>(name: string): T {
  const container = getGlobalContainer()
  return container.get<T>(name)
}

/**
 * 便捷的服务检查函数
 */
export function hasService(name: string): boolean {
  const container = getGlobalContainer()
  return container.has(name)
}
