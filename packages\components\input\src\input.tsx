import { computed, defineComponent } from 'vue'
import { ElInput } from 'element-plus'
import { neInputProps } from './types'

const NeInput = defineComponent({
  name: 'NeInput',
  props: neInputProps,
  setup(props) {
    const { ...restProps } = props
    const realProps = computed(() => {
      return {
        ...restProps,
        placeholder: props.placeholder || '请输入',
      }
    })
    return () => <ElInput {...realProps.value} />
  },
})

export default NeInput
