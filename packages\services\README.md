# @neue-plus/services

一个强大的服务层框架，提供统一的 API 调用接口，特别针对 OData 协议进行了优化。

## 特性

- 🚀 **统一的 API 接口** - 支持 REST 和 OData 协议
- 🎯 **强大的 OData 客户端** - 完整的 CRUD 操作支持
- � **表达式解析** - 支持动态参数和上下文变量
- 📦 **TypeScript 支持** - 完整的类型定义
- 🛡️ **错误处理** - 统一的错误处理机制
- � **客户端缓存** - 自动缓存 OData 客户端实例

## 安装

```bash
pnpm add @neue-plus/services axios
```

## 基础用法

### 1. 使用 executeApi（推荐用于简单场景）

```typescript
import { executeApi } from '@neue-plus/services'

// REST API 调用
const restResult = await executeApi({
  url: '/api/users',
  method: 'get',
  params: { page: 1, size: 10 },
})

// OData API 调用
const odataResult = await executeApi({
  url: 'https://services.odata.org/V4/TripPinServiceRW/People',
  method: 'get',
  protocol: 'odata',
  odata: {
    select: 'FirstName,LastName,Gender',
    top: 5,
    filter: 'Gender eq "Female"',
  },
  dataPath: 'value',
})
```

### 2. 使用 OData 客户端（推荐用于复杂场景）

```typescript
import { createODataClient } from '@neue-plus/services'

// 创建客户端
const client = createODataClient({
  baseUrl: 'https://services.odata.org/V4/TripPinServiceRW',
  timeout: 30000,
  headers: {
    'Custom-Header': 'value',
  },
})

// 查询数据
const result = await client.query('People', {
  select: 'FirstName,LastName,Gender',
  filter: 'Gender eq "Female"',
  orderby: 'FirstName asc',
  top: 10,
  skip: 0,
  count: true,
})

console.log(result)
// {
//   data: [...],
//   total: 100,
//   page: 1,
//   pageSize: 10,
//   hasNext: true,
//   hasPrev: false,
//   success: true
// }
```

### 3. 使用 executeApi 进行 CRUD 操作

```typescript
// 创建实体 (POST)
const createResult = await executeApi({
  url: 'https://api.example.com/odata/People',
  method: 'post',
  protocol: 'odata',
  body: {
    FirstName: 'John',
    LastName: 'Doe',
    Gender: 'Male',
  },
})

// 更新实体 (PUT)
const updateResult = await executeApi({
  url: 'https://api.example.com/odata/People',
  method: 'put',
  protocol: 'odata',
  params: { id: 'john-doe' },
  body: {
    FirstName: 'Johnny',
    LastName: 'Doe',
    Gender: 'Male',
  },
})

// 部分更新 (PATCH)
const patchResult = await executeApi({
  url: 'https://api.example.com/odata/People',
  method: 'patch',
  protocol: 'odata',
  params: { id: 'john-doe' },
  body: {
    FirstName: 'Jonathan',
  },
})

// 删除实体 (DELETE)
const deleteResult = await executeApi({
  url: 'https://api.example.com/odata/People',
  method: 'delete',
  protocol: 'odata',
  params: { id: 'john-doe' },
})
```

## OData 客户端详细用法

### 查询操作

```typescript
// 基础查询
const people = await client.query('People', {
  select: 'FirstName,LastName',
  top: 5,
})

// 复杂查询
const filteredPeople = await client.query('People', {
  select: 'FirstName,LastName,Gender,Age',
  filter: 'Age gt 25 and Gender eq "Female"',
  orderby: 'Age desc',
  expand: 'Friends',
  top: 10,
  skip: 20,
  count: true,
  search: 'John',
})

// 分页查询
const pagedResult = await client.query('People', {
  select: 'FirstName,LastName,Gender',
  top: 10,
  skip: 20,
  count: true,
})

console.log(`第 ${pagedResult.page} 页，共 ${pagedResult.total} 条记录`)
console.log(`是否有下一页: ${pagedResult.hasNext}`)
```

### CRUD 操作

```typescript
// 获取单个实体
const person = await client.get('russellwhyte', 'People', {
  select: 'FirstName,LastName',
  expand: 'Friends',
})

// 创建实体
const newPerson = await client.create(
  {
    FirstName: 'John',
    LastName: 'Doe',
    Gender: 'Male',
  },
  'People'
)

// 更新实体（PUT - 完全替换）
const updatedPerson = await client.update(
  'johnDoe',
  {
    FirstName: 'Johnny',
    LastName: 'Doe',
  },
  'People'
)

// 部分更新（PATCH - 只更新指定字段）
const patchedPerson = await client.patch(
  'johnDoe',
  {
    FirstName: 'Jonathan',
  },
  'People'
)

// 删除实体
const deleted = await client.delete('johnDoe', 'People')
```

### 函数和操作调用

```typescript
// 调用函数（GET 请求）
const functionResult = await client.callFunction('GetNearestAirport', {
  lat: 33.0,
  lon: -118.0,
})

// 调用操作（POST 请求）
const actionResult = await client.callAction('ResetDataSource', {
  confirm: true,
})
```

### 认证配置

```typescript
// Basic 认证
const client = createODataClient({
  baseUrl: 'https://api.example.com/odata',
  auth: {
    type: 'basic',
    credentials: {
      username: 'user',
      password: 'pass',
    },
  },
})

// Bearer Token 认证
const client = createODataClient({
  baseUrl: 'https://api.example.com/odata',
  auth: {
    type: 'bearer',
    credentials: {
      token: 'your-jwt-token',
    },
  },
})
```

### 表达式解析

支持在 API 配置中使用动态表达式：

```typescript
const context = {
  userId: 123,
  department: 'IT',
  currentDate: new Date().toISOString(),
}

const result = await executeApi(
  {
    url: '/api/users/{{userId}}',
    method: 'get',
    params: {
      department: '{{department}}',
      since: '{{currentDate}}',
    },
    odata: {
      filter:
        'Department eq "{{department}}" and CreatedDate gt {{currentDate}}',
    },
  },
  context
)
```

### 错误处理

```typescript
import { setErrorHandler } from '@neue-plus/services'

// 设置全局错误处理器
setErrorHandler((message: string) => {
  console.error('API Error:', message)
  // 可以集成到你的通知系统
})
```

### TypeScript 类型

```typescript
import type {
  ApiSchema,
  ODataQueryOptions,
  ODataServiceConfig,
  PaginatedResponse,
  ODataEntity,
} from '@neue-plus/services'

// 定义你的实体类型
interface Person extends ODataEntity {
  FirstName: string
  LastName: string
  Gender: string
  Age: number
}

// 使用类型化的客户端
const result = await client.query<Person>('People', {
  select: 'FirstName,LastName,Gender,Age',
  top: 10,
})

// result.data 现在是 Person[] 类型
```

### 最佳实践

1. **客户端复用**: OData 客户端会自动缓存，相同 baseUrl 的客户端会被复用
2. **错误处理**: 始终处理可能的错误情况
3. **类型安全**: 使用 TypeScript 类型定义确保类型安全
4. **分页处理**: 使用 `top` 和 `skip` 参数进行分页
5. **性能优化**: 使用 `select` 参数只获取需要的字段

### With Template Parameters

```typescript
const apiSchema = {
  url: 'https://api.example.com/users/{{userId}}',
  method: 'get' as const,
  headers: {
    Authorization: 'Bearer {{token}}',
  },
  params: {
    page: '{{currentPage}}',
    limit: 10,
  },
}

const context = {
  userId: 123,
  token: 'your-auth-token',
  currentPage: 1,
}

const result = await executeApi(apiSchema, context)
```

### OData Support

```typescript
const odataSchema = {
  url: 'https://api.example.com/odata/Products',
  method: 'get' as const,
  protocol: 'odata' as const,
  odata: {
    filter: "Name eq '{{productName}}'",
    select: 'Id,Name,Price',
    top: 10,
    orderby: 'Name asc',
  },
}

const context = {
  productName: 'Laptop',
}

const result = await executeApi(odataSchema, context)
```

## API Reference

### Types

#### `ApiSchema`

```typescript
interface ApiSchema {
  url: string
  method: 'get' | 'post' | 'put' | 'delete'
  protocol?: 'rest' | 'odata'
  headers?: Record<string, any>
  params?: Record<string, any>
  body?: any
  dataPath?: string
  map?: { label: string; value: string }
  odata?: {
    filter?: string
    orderby?: string
    select?: string
    expand?: string
    top?: number
    skip?: number
    count?: boolean
  }
}
```

#### `ApiResponse`

```typescript
interface ApiResponse<T = any> {
  data: T
  error?: string
  status?: number
  [key: string]: any
}
```

### Functions

#### `executeApi(schema, context)`

Executes an API call based on the provided schema and context.

- **Parameters:**

  - `schema: ApiSchema` - The API configuration
  - `context: Record<string, any>` - Variables for template substitution

- **Returns:** `Promise<ApiResponse>`

#### `parseExpression(obj, context)`

Parses template expressions in objects, arrays, or strings.

- **Parameters:**

  - `obj: any` - The object to parse
  - `context: Record<string, any>` - Variables for substitution

- **Returns:** `any` - Parsed object with substituted values

#### `setErrorHandler(handler)`

Sets a custom error handler for API errors.

- **Parameters:**
  - `handler: (message: string) => void` - Custom error handler function

```typescript
import { setErrorHandler } from '@neue-plus/services'

setErrorHandler((message) => {
  // Custom error handling logic
  console.error('API Error:', message)
  // Show toast, log to service, etc.
})
```

## Advanced Usage

### Custom Data Path

```typescript
const schema = {
  url: 'https://api.example.com/data',
  method: 'get' as const,
  dataPath: 'result.items', // Extract data from nested path
}
```

### POST Request with Body

```typescript
const schema = {
  url: 'https://api.example.com/users',
  method: 'post' as const,
  headers: {
    'Content-Type': 'application/json',
  },
  body: {
    name: '{{userName}}',
    email: '{{userEmail}}',
  },
}

const context = {
  userName: 'John Doe',
  userEmail: '<EMAIL>',
}
```

## Element Plus Integration

If you're using Element Plus in your project, error messages will automatically use `ElMessage.error()`. If Element Plus is not available, errors will fall back to console logging or your custom error handler.

## Development

```bash
# Install dependencies
pnpm install

# Build the package
pnpm build

# Run tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Type checking
pnpm typecheck
```

## License

MIT

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
