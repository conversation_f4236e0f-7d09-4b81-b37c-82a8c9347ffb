# 🔧 数据提取功能改进 - 使用 lodash-unified

## 概述

基于你的建议，我们使用 `lodash-unified` 的 `get` 方法来改进数据提取功能，提供更安全、更强大的数据访问能力。

## 🔍 发现的问题

### 1. **重复的数据提取逻辑**
```typescript
// 在 REST 和 OData 适配器中都有相同的代码
private extractDataByPath(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined
  }, obj)
}
```

### 2. **不够健壮的路径解析**
- 不支持数组索引：`data.items[0]`
- 不支持复杂路径：`response.data.items[0].name`
- 错误处理不完善
- 没有默认值支持

### 3. **OData 数据结构处理不完整**
- 只处理标准的 `value` 字段
- 不支持旧版 OData 格式 `d.results`
- 错误信息提取不完整

## 🚀 改进方案

### 1. **使用 lodash-unified 的 get 方法**

#### **基础数据提取**
```typescript
import { get } from 'lodash-unified'

static extractDataByPath(obj: any, path: string, defaultValue?: any): any {
  if (!obj || !path) return defaultValue !== undefined ? defaultValue : obj
  return get(obj, path, defaultValue)
}
```

**优势**：
- ✅ 支持复杂路径：`'data.items[0].name'`
- ✅ 支持数组索引：`'users[0]'`
- ✅ 安全的属性访问，不会抛出错误
- ✅ 内置默认值支持
- ✅ 性能优化，经过充分测试

#### **批量路径尝试**
```typescript
static extractDataByPaths(obj: any, paths: string[], defaultValue?: any): any {
  if (!obj || !paths || paths.length === 0) 
    return defaultValue !== undefined ? defaultValue : obj
  
  for (const path of paths) {
    const result = get(obj, path)
    if (result !== undefined) {
      return result
    }
  }
  
  return defaultValue !== undefined ? defaultValue : obj
}
```

**优势**：
- ✅ 支持多种数据格式的兼容性
- ✅ 按优先级尝试不同路径
- ✅ 提高 API 响应格式的容错性

### 2. **改进的 OData 数据处理**

#### **多格式支持**
```typescript
// 处理 OData 集合响应 - 支持多种可能的数据路径
const collectionData = this.extractDataByPaths(rawData, [
  'value',           // 标准 OData 格式
  'd.results',       // 旧版 OData 格式
  'data',            // 自定义格式
  'items'            // 通用集合格式
])
```

#### **增强的错误处理**
```typescript
// 处理 OData 错误响应
const errorMessage = 
  get(rawData, 'error.message') || 
  get(rawData, 'error.innererror.message')
if (errorMessage) {
  throw new Error(errorMessage)
}
```

#### **智能元数据过滤**
```typescript
// 处理 OData 单条记录响应（过滤掉元数据字段）
const metadataKeys = ['@odata.context', '@odata.etag', '@odata.id', '@odata.type']
const actualData = Object.keys(rawData)
  .filter(key => !metadataKeys.includes(key))
  .reduce((obj, key) => {
    obj[key] = rawData[key]
    return obj
  }, {} as any)
```

### 3. **统一的数据提取接口**

#### **消除重复代码**
```typescript
// 修改前：每个适配器都有自己的实现
// REST 适配器
private extractDataByPath(obj: any, path: string): any { /* 重复代码 */ }

// OData 适配器  
private extractDataByPath(obj: any, path: string): any { /* 重复代码 */ }

// 修改后：统一使用 ResponseProcessor 的方法
const extractedData = ResponseProcessor.extractDataByPath(response, restConfig.dataPath)
```

## 📊 支持的路径格式

### 1. **简单属性访问**
```typescript
get(obj, 'name')                    // obj.name
get(obj, 'user.name')              // obj.user.name
get(obj, 'user.profile.email')     // obj.user.profile.email
```

### 2. **数组索引访问**
```typescript
get(obj, 'users[0]')               // obj.users[0]
get(obj, 'users[0].name')          // obj.users[0].name
get(obj, 'data.items[0].id')       // obj.data.items[0].id
```

### 3. **混合路径**
```typescript
get(obj, 'response.data.users[0].profile.settings.theme')
```

### 4. **默认值支持**
```typescript
get(obj, 'user.name', 'Unknown')           // 如果路径不存在返回 'Unknown'
get(obj, 'users', [])                      // 如果路径不存在返回空数组
get(obj, 'config.enabled', false)         // 如果路径不存在返回 false
```

## 🎯 实际应用示例

### 1. **REST API 数据提取**
```typescript
// API 响应格式 1
{
  "data": {
    "users": [{ "id": 1, "name": "John" }]
  }
}

// API 响应格式 2  
{
  "result": {
    "items": [{ "id": 1, "name": "John" }]
  }
}

// 统一处理
const users = ResponseProcessor.extractDataByPaths(response, [
  'data.users',      // 格式 1
  'result.items',    // 格式 2
  'users',           // 直接格式
  'items'            // 备选格式
], [])
```

### 2. **OData 多版本兼容**
```typescript
// OData v4 格式
{
  "@odata.context": "...",
  "value": [{ "id": 1, "name": "John" }]
}

// OData v2 格式
{
  "d": {
    "results": [{ "id": 1, "name": "John" }]
  }
}

// 统一处理
const data = ResponseProcessor.extractDataByPaths(response, [
  'value',           // v4 格式
  'd.results',       // v2 格式
  'data',            // 自定义格式
  'items'            // 通用格式
])
```

### 3. **复杂嵌套数据**
```typescript
// 复杂 API 响应
{
  "response": {
    "status": "success",
    "data": {
      "pagination": { "total": 100 },
      "items": [
        {
          "user": {
            "profile": {
              "personal": {
                "name": "John Doe"
              }
            }
          }
        }
      ]
    }
  }
}

// 提取用户名
const userName = ResponseProcessor.extractDataByPath(
  response, 
  'response.data.items[0].user.profile.personal.name',
  'Unknown User'
)
```

## ✅ 改进效果

### 1. **代码简化**
- ❌ 删除了重复的 `extractDataByPath` 方法
- ✅ 统一使用 `lodash-unified` 的 `get` 方法
- ✅ 减少了 50+ 行重复代码

### 2. **功能增强**
- ✅ 支持数组索引访问：`data.items[0]`
- ✅ 支持复杂路径：`response.data.items[0].name`
- ✅ 内置默认值支持
- ✅ 更好的错误处理

### 3. **兼容性提升**
- ✅ 支持多种 OData 版本格式
- ✅ 支持不同的 API 响应结构
- ✅ 向后兼容现有代码

### 4. **性能优化**
- ✅ 使用经过优化的 lodash 实现
- ✅ 避免了自定义路径解析的性能开销
- ✅ 更好的内存使用效率

## 🔄 迁移指南

### 1. **适配器中的使用**
```typescript
// 修改前
const extractedData = this.extractDataByPath(response, config.dataPath)

// 修改后
const extractedData = ResponseProcessor.extractDataByPath(response, config.dataPath, response)
```

### 2. **自定义数据提取**
```typescript
// 修改前
const data = response.data?.items || response.items || []

// 修改后
const data = ResponseProcessor.extractDataByPaths(response, [
  'data.items',
  'items',
  'value',
  'results'
], [])
```

### 3. **错误安全的访问**
```typescript
// 修改前
const userName = response.user && response.user.profile && response.user.profile.name

// 修改后
const userName = ResponseProcessor.extractDataByPath(response, 'user.profile.name', 'Unknown')
```

## 🎉 总结

使用 `lodash-unified` 的 `get` 方法显著改进了数据提取功能：

1. **✅ 更安全**：不会因为路径不存在而抛出错误
2. **✅ 更强大**：支持复杂路径和数组索引
3. **✅ 更简洁**：消除了重复代码
4. **✅ 更兼容**：支持多种数据格式
5. **✅ 更可靠**：使用经过充分测试的库

这个改进让数据提取更加健壮和灵活，为处理各种 API 响应格式提供了强大的基础。
