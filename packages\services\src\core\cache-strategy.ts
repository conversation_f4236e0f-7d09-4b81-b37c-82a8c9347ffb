/**
 * 缓存策略处理器
 * 实现不同的缓存策略逻辑
 */

import {
  CacheStrategy,
  RequestCacheManager,
  getGlobalCacheManager,
} from './request-cache'
import type { ApiConfig, ApiResponse, RequestContext } from './types'

// 缓存策略执行器接口
export interface CacheStrategyExecutor {
  execute<T = any>(
    config: ApiConfig,
    context: RequestContext,
    requestFn: () => Promise<ApiResponse<T>>,
    cacheManager?: RequestCacheManager
  ): Promise<ApiResponse<T>>
}

// 缓存优先策略
export class CacheFirstStrategy implements CacheStrategyExecutor {
  async execute<T = any>(
    config: ApiConfig,
    context: RequestContext,
    requestFn: () => Promise<ApiResponse<T>>,
    cacheManager = getGlobalCacheManager()
  ): Promise<ApiResponse<T>> {
    const cacheKey = cacheManager.generateCacheKey(config, context)

    // 先尝试从缓存获取
    const cachedData = cacheManager.get<ApiResponse<T>>(cacheKey)
    if (cachedData) {
      return {
        ...cachedData,
        fromCache: true,
      } as ApiResponse<T> & { fromCache: boolean }
    }

    // 缓存未命中，执行请求并缓存结果
    return cacheManager.dedupe(cacheKey, async () => {
      const result = await requestFn()
      if (result.success) {
        cacheManager.set(cacheKey, result)
      }
      return result
    })
  }
}

// 网络优先策略
export class NetworkFirstStrategy implements CacheStrategyExecutor {
  async execute<T = any>(
    config: ApiConfig,
    context: RequestContext,
    requestFn: () => Promise<ApiResponse<T>>,
    cacheManager = getGlobalCacheManager()
  ): Promise<ApiResponse<T>> {
    const cacheKey = cacheManager.generateCacheKey(config, context)

    try {
      // 先尝试网络请求
      const result = await cacheManager.dedupe(cacheKey, requestFn)

      // 请求成功，更新缓存
      if (result.success) {
        cacheManager.set(cacheKey, result)
      }

      return result
    } catch (error) {
      // 网络请求失败，尝试使用缓存
      const cachedData = cacheManager.get<ApiResponse<T>>(cacheKey)
      if (cachedData) {
        return {
          ...cachedData,
          fromCache: true,
          error: `Network failed, using cache: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`,
        } as ApiResponse<T> & { fromCache: boolean }
      }

      // 缓存也没有，抛出错误
      throw error
    }
  }
}

// 仅缓存策略
export class CacheOnlyStrategy implements CacheStrategyExecutor {
  async execute<T = any>(
    config: ApiConfig,
    context: RequestContext,
    requestFn: () => Promise<ApiResponse<T>>,
    cacheManager = getGlobalCacheManager()
  ): Promise<ApiResponse<T>> {
    const cacheKey = cacheManager.generateCacheKey(config, context)
    const cachedData = cacheManager.get<ApiResponse<T>>(cacheKey)

    if (cachedData) {
      return {
        ...cachedData,
        fromCache: true,
      } as ApiResponse<T> & { fromCache: boolean }
    }

    // 缓存未命中，返回错误
    return {
      data: null,
      success: false,
      error: 'No cached data available',
      fromCache: false,
    } as ApiResponse<T> & { fromCache: boolean }
  }
}

// 仅网络策略
export class NetworkOnlyStrategy implements CacheStrategyExecutor {
  async execute<T = any>(
    config: ApiConfig,
    context: RequestContext,
    requestFn: () => Promise<ApiResponse<T>>
  ): Promise<ApiResponse<T>> {
    // 直接执行网络请求，不使用缓存
    return requestFn()
  }
}

// 过期重新验证策略
export class StaleWhileRevalidateStrategy implements CacheStrategyExecutor {
  async execute<T = any>(
    config: ApiConfig,
    context: RequestContext,
    requestFn: () => Promise<ApiResponse<T>>,
    cacheManager = getGlobalCacheManager()
  ): Promise<ApiResponse<T>> {
    const cacheKey = cacheManager.generateCacheKey(config, context)
    const cachedData = cacheManager.get<ApiResponse<T>>(cacheKey)

    // 如果有缓存数据，立即返回
    if (cachedData) {
      // 后台异步更新缓存
      this.revalidateInBackground(cacheKey, requestFn, cacheManager)

      return {
        ...cachedData,
        fromCache: true,
        stale: true,
      } as ApiResponse<T> & { fromCache: boolean; stale: boolean }
    }

    // 没有缓存数据，执行请求并缓存
    return cacheManager.dedupe(cacheKey, async () => {
      const result = await requestFn()
      if (result.success) {
        cacheManager.set(cacheKey, result)
      }
      return result
    })
  }

  private async revalidateInBackground<T>(
    cacheKey: string,
    requestFn: () => Promise<ApiResponse<T>>,
    cacheManager: RequestCacheManager
  ): Promise<void> {
    try {
      const result = await requestFn()
      if (result.success) {
        cacheManager.set(cacheKey, result)
      }
    } catch (error) {
      // 后台更新失败，保持现有缓存
      console.warn('Background revalidation failed:', error)
    }
  }
}

// 缓存策略工厂
export class CacheStrategyFactory {
  private static strategies = new Map<CacheStrategy, CacheStrategyExecutor>([
    [CacheStrategy.CACHE_FIRST, new CacheFirstStrategy()],
    [CacheStrategy.NETWORK_FIRST, new NetworkFirstStrategy()],
    [CacheStrategy.CACHE_ONLY, new CacheOnlyStrategy()],
    [CacheStrategy.NETWORK_ONLY, new NetworkOnlyStrategy()],
    [CacheStrategy.STALE_WHILE_REVALIDATE, new StaleWhileRevalidateStrategy()],
  ])

  static getStrategy(strategy: CacheStrategy): CacheStrategyExecutor {
    const executor = this.strategies.get(strategy)
    if (!executor) {
      throw new Error(`Unknown cache strategy: ${strategy}`)
    }
    return executor
  }

  static registerStrategy(
    strategy: CacheStrategy,
    executor: CacheStrategyExecutor
  ): void {
    this.strategies.set(strategy, executor)
  }
}

// 缓存策略配置
export interface CacheStrategyConfig {
  strategy?: CacheStrategy
  ttl?: number
  enabled?: boolean
  conditions?: {
    methods?: string[] // 只对特定 HTTP 方法启用缓存
    protocols?: string[] // 只对特定协议启用缓存
    urlPatterns?: RegExp[] // 只对匹配的 URL 模式启用缓存
  }
}

// 缓存策略管理器
export class CacheStrategyManager {
  private defaultStrategy: CacheStrategy = CacheStrategy.CACHE_FIRST
  private strategyConfigs = new Map<string, CacheStrategyConfig>()

  /**
   * 设置默认缓存策略
   */
  setDefaultStrategy(strategy: CacheStrategy): void {
    this.defaultStrategy = strategy
  }

  /**
   * 为特定配置设置缓存策略
   */
  setStrategyForPattern(pattern: string, config: CacheStrategyConfig): void {
    this.strategyConfigs.set(pattern, config)
  }

  /**
   * 获取适用的缓存策略
   */
  getApplicableStrategy(config: ApiConfig): CacheStrategy {
    // 检查是否有匹配的策略配置
    for (const [pattern, strategyConfig] of this.strategyConfigs.entries()) {
      if (this.matchesPattern(config, pattern, strategyConfig)) {
        return strategyConfig.strategy || this.defaultStrategy
      }
    }

    return this.defaultStrategy
  }

  /**
   * 检查配置是否匹配模式
   */
  private matchesPattern(
    config: ApiConfig,
    pattern: string,
    strategyConfig: CacheStrategyConfig
  ): boolean {
    const conditions = strategyConfig.conditions

    if (!conditions) {
      return config.url.includes(pattern)
    }

    // 检查 HTTP 方法
    if (conditions.methods && !conditions.methods.includes(config.method)) {
      return false
    }

    // 检查协议
    if (
      conditions.protocols &&
      !conditions.protocols.includes(config.protocol || 'rest')
    ) {
      return false
    }

    // 检查 URL 模式
    if (conditions.urlPatterns) {
      const matchesUrl = conditions.urlPatterns.some((regex) =>
        regex.test(config.url)
      )
      if (!matchesUrl) {
        return false
      }
    }

    return true
  }

  /**
   * 执行缓存策略
   */
  async executeStrategy<T = any>(
    config: ApiConfig,
    context: RequestContext,
    requestFn: () => Promise<ApiResponse<T>>,
    cacheManager?: RequestCacheManager
  ): Promise<ApiResponse<T>> {
    const strategy = this.getApplicableStrategy(config)
    const executor = CacheStrategyFactory.getStrategy(strategy)

    return executor.execute(config, context, requestFn, cacheManager)
  }
}

// 全局缓存策略管理器
let globalStrategyManager: CacheStrategyManager | null = null

/**
 * 获取全局缓存策略管理器
 */
export function getGlobalStrategyManager(): CacheStrategyManager {
  if (!globalStrategyManager) {
    globalStrategyManager = new CacheStrategyManager()
  }
  return globalStrategyManager
}

/**
 * 设置全局缓存策略管理器
 */
export function setGlobalStrategyManager(manager: CacheStrategyManager): void {
  globalStrategyManager = manager
}
