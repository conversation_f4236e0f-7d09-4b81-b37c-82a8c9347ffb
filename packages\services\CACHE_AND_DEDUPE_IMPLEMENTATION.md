# 请求去重和缓存策略实现

## 概述

我们为 `@neue-plus/services` 添加了完整的请求去重和缓存策略功能，提供了企业级的性能优化和用户体验改进。

## 核心功能

### 1. 请求缓存管理器 (RequestCacheManager)

#### 特性

- **智能缓存**: 基于请求配置和上下文自动生成缓存键
- **TTL 支持**: 可配置的缓存过期时间
- **大小限制**: 防止内存泄漏的缓存大小控制
- **自动清理**: 定期清理过期缓存项
- **统计信息**: 提供详细的缓存命中率和使用统计

#### 配置选项

```typescript
interface CacheConfig {
  ttl?: number // 缓存时间（毫秒），默认 5 分钟
  maxSize?: number // 最大缓存数量，默认 100
  enabled?: boolean // 是否启用缓存，默认 true
}
```

#### 使用示例

```typescript
import { createCacheManager, getGlobalCacheManager } from '@neue-plus/services'

// 创建自定义缓存管理器
const cacheManager = createCacheManager({
  ttl: 10 * 60 * 1000, // 10 分钟
  maxSize: 200,
  enabled: true,
})

// 使用全局缓存管理器
const globalCache = getGlobalCacheManager()
const stats = globalCache.getStats()
console.log('缓存统计:', stats)
```

### 2. 请求去重功能

#### 特性

- **自动去重**: 相同请求在进行中时自动合并
- **超时保护**: 防止请求长时间挂起
- **Promise 共享**: 多个相同请求共享同一个 Promise

#### 实现原理

```typescript
// 同时发起的相同请求会被去重
const promises = Array.from({ length: 3 }, () =>
  executeApi({
    url: '/api/users',
    method: 'get',
  })
)

// 实际只会发起一次网络请求
const results = await Promise.all(promises)
```

### 3. 缓存策略系统

#### 支持的策略

##### 1. Cache First (缓存优先)

```typescript
const result = await executeApi({
  url: '/api/users',
  method: 'get',
  cache: {
    strategy: 'cache-first',
    ttl: 60000,
    enabled: true,
  },
})
```

- **行为**: 优先返回缓存数据，缓存未命中时请求网络
- **适用场景**: 数据变化不频繁，对实时性要求不高的场景

##### 2. Network First (网络优先)

```typescript
const result = await executeApi({
  url: '/api/users',
  method: 'get',
  cache: {
    strategy: 'network-first',
    ttl: 60000,
    enabled: true,
  },
})
```

- **行为**: 优先请求网络，失败时使用缓存数据
- **适用场景**: 需要最新数据，但允许降级到缓存的场景

##### 3. Cache Only (仅缓存)

```typescript
const result = await executeApi({
  url: '/api/users',
  method: 'get',
  cache: {
    strategy: 'cache-only',
    enabled: true,
  },
})
```

- **行为**: 仅使用缓存数据，不发起网络请求
- **适用场景**: 离线模式或确保不产生网络流量的场景

##### 4. Network Only (仅网络)

```typescript
const result = await executeApi({
  url: '/api/users',
  method: 'get',
  cache: {
    strategy: 'network-only',
    enabled: true,
  },
})
```

- **行为**: 仅发起网络请求，不使用缓存
- **适用场景**: 需要实时数据，不允许使用缓存的场景

##### 5. Stale While Revalidate (过期重新验证)

```typescript
const result = await executeApi({
  url: '/api/users',
  method: 'get',
  cache: {
    strategy: 'stale-while-revalidate',
    ttl: 60000,
    enabled: true,
  },
})
```

- **行为**: 立即返回缓存数据，后台异步更新缓存
- **适用场景**: 需要快速响应，同时保持数据相对新鲜的场景

### 4. 策略管理器 (CacheStrategyManager)

#### 特性

- **默认策略**: 可配置的全局默认缓存策略
- **模式匹配**: 为特定 URL 模式设置不同的缓存策略
- **条件配置**: 基于 HTTP 方法、协议等条件的策略配置

#### 使用示例

```typescript
import { getGlobalStrategyManager } from '@neue-plus/services'

const strategyManager = getGlobalStrategyManager()

// 设置默认策略
strategyManager.setDefaultStrategy('cache-first')

// 为特定模式设置策略
strategyManager.setStrategyForPattern('/api/users', {
  strategy: 'network-first',
  ttl: 30000,
  conditions: {
    methods: ['get'],
    protocols: ['rest'],
  },
})
```

## 集成到 API 服务

### 1. 自动集成

所有通过 `executeApi` 的请求都会自动应用缓存和去重功能：

```typescript
import { executeApi } from '@neue-plus/services'

// 自动应用缓存策略
const result = await executeApi({
  url: '/api/users',
  method: 'get',
  cache: {
    strategy: 'cache-first',
    ttl: 60000,
  },
})
```

### 2. 服务级别配置

```typescript
import { createApiService, createCacheManager } from '@neue-plus/services'

const customCache = createCacheManager({
  ttl: 5 * 60 * 1000,
  maxSize: 500,
})

const apiService = createApiService(customCache)
```

## 响应数据增强

缓存功能会在响应数据中添加额外的元信息：

```typescript
interface ApiResponse<T> {
  data: T | T[]
  success: boolean
  // 缓存相关字段
  fromCache?: boolean // 数据是否来自缓存
  stale?: boolean // 数据是否过期（SWR 策略）
  cacheKey?: string // 缓存键
}
```

## 性能优化

### 1. 内存管理

- **LRU 淘汰**: 缓存满时淘汰最旧的项
- **定期清理**: 每分钟清理过期缓存
- **大小限制**: 防止无限制增长

### 2. 网络优化

- **请求去重**: 减少重复网络请求
- **智能缓存**: 减少不必要的网络流量
- **后台更新**: SWR 策略提供快速响应

## 监控和调试

### 1. 缓存统计

```typescript
import { getGlobalCacheManager } from '@neue-plus/services'

const stats = getGlobalCacheManager().getStats()
console.log('缓存统计:', {
  total: stats.total, // 总缓存项数
  valid: stats.valid, // 有效缓存项数
  expired: stats.expired, // 过期缓存项数
  pending: stats.pending, // 进行中的请求数
  hitRate: stats.hitRate, // 缓存命中率
})
```

### 2. 缓存管理

```typescript
// 清空所有缓存
getGlobalCacheManager().clear()

// 删除特定缓存
getGlobalCacheManager().delete(cacheKey)

// 更新缓存配置
getGlobalCacheManager().updateConfig({
  ttl: 10 * 60 * 1000,
  maxSize: 200,
})
```

## 演示功能

在 play 项目中提供了完整的缓存功能演示：

### 1. 缓存策略演示

- **缓存优先策略**: 演示缓存命中和未命中的行为
- **网络优先策略**: 演示网络请求和缓存降级
- **请求去重**: 演示多个相同请求的去重效果

### 2. 缓存管理演示

- **缓存统计**: 显示当前缓存状态和统计信息
- **缓存清理**: 演示手动清理缓存的功能

## 最佳实践

### 1. 策略选择

- **静态数据**: 使用 `cache-first` 策略
- **动态数据**: 使用 `network-first` 策略
- **实时数据**: 使用 `network-only` 策略
- **用户体验优先**: 使用 `stale-while-revalidate` 策略

### 2. TTL 配置

- **用户数据**: 1-5 分钟
- **配置数据**: 10-30 分钟
- **静态资源**: 1-24 小时

### 3. 缓存键设计

- 包含所有影响响应的参数
- 考虑用户上下文（如用户 ID）
- 避免过于复杂的键结构

## 总结

通过添加请求去重和缓存策略功能，`@neue-plus/services` 现在提供了：

- 🚀 **更好的性能**: 减少重复请求和网络流量
- 🛡️ **更强的稳定性**: 网络故障时的缓存降级
- 📈 **更好的用户体验**: 快速响应和智能缓存
- 🔧 **灵活的配置**: 多种缓存策略和精细化控制
- 📊 **完善的监控**: 详细的统计信息和调试工具

这些功能为构建高性能、用户友好的企业级应用提供了坚实的基础。
