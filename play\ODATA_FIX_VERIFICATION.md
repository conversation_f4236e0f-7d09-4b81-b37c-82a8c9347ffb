# 🔧 OData 配置问题修复验证

## 问题描述

在使用 ApiRequestButton 组件发送 OData 请求时，发现传递的 `odata` 参数没有正确传递到适配器中，导致查询选项无效。

## 问题定位

### 1. 原始问题
- 在 `odata-adapter.ts` 的 `handleGet` 方法中，代码检查 `config.query`
- 但在 `ApiRequestButton.vue` 中，OData 配置被注释掉了
- 导致 OData 查询选项没有被正确传递

### 2. 根本原因
```typescript
// 问题代码 - ApiRequestButton.vue (第 422-425 行)
// if (Object.keys(odata).length > 0) {
//   config.odata = odata
// }

// 问题代码 - odata-adapter.ts (第 128 行)
if (config.query) {  // 应该检查 config.query 而不是 config.odata
```

## 修复方案

### 1. 修复 ApiRequestButton.vue
```typescript
// 修复后的代码
if (Object.keys(odata).length > 0) {
  config.query = odata  // 使用 query 属性而不是 odata
}
```

### 2. 确认 odata-adapter.ts
```typescript
// 确认代码正确
if (config.query) {
  const result = await client.query(entitySet, config.query, context)
}
```

## 类型定义确认

### ODataApiConfig 接口
```typescript
export interface ODataApiConfig extends BaseRequestConfig {
  protocol: 'odata'
  params?: Record<string, any>
  body?: any
  dataPath?: string
  query?: ODataQueryOptions  // ✅ 正确的属性名
}
```

### LegacyApiSchema 接口
```typescript
export interface LegacyApiSchema {
  // ...
  odata?: ODataQueryOptions  // ✅ 向后兼容的属性名
}
```

## 测试验证

### 1. 测试步骤
1. 打开 http://localhost:5174
2. 在 ApiRequestButton 组件中：
   - 选择 GET 方法
   - 选择 OData 协议
   - 输入 URL: `https://services.odata.org/V4/TripPinServiceRW/People`
   - 在 OData 标签页中设置：
     - `$select`: `FirstName,LastName,Gender`
     - `$filter`: `Gender eq 'Female'`
     - `$top`: `5`
3. 点击"发送请求"
4. 查看控制台日志和响应结果

### 2. 预期结果
- 控制台应该显示正确的配置信息
- 响应应该包含过滤后的数据（只有女性用户）
- 响应应该只包含指定的字段（FirstName, LastName, Gender）
- 响应应该限制为 5 条记录

### 3. 验证点
```javascript
// 控制台应该显示类似的配置
{
  url: "https://services.odata.org/V4/TripPinServiceRW/People",
  method: "get",
  protocol: "odata",
  query: {
    select: "FirstName,LastName,Gender",
    filter: "Gender eq 'Female'",
    top: 5
  }
}
```

## 修复前后对比

### 修复前
```typescript
// ApiRequestButton.vue - OData 配置被注释
// if (Object.keys(odata).length > 0) {
//   config.odata = odata
// }

// 结果：config.query 为 undefined
// odata-adapter.ts 中 if (config.query) 条件为 false
// 查询选项被忽略
```

### 修复后
```typescript
// ApiRequestButton.vue - OData 配置正确设置
if (Object.keys(odata).length > 0) {
  config.query = odata
}

// 结果：config.query 包含查询选项
// odata-adapter.ts 中 if (config.query) 条件为 true
// 查询选项被正确传递
```

## 相关文件

### 修改的文件
1. `play/src/components/ApiRequestButton.vue` - 第 422 行
   - 取消注释 OData 配置代码
   - 使用 `config.query` 而不是 `config.odata`

2. `packages/services/src/adapters/odata-adapter.ts` - 第 128 行
   - 确认使用 `config.query` 检查查询选项

### 相关类型定义
1. `packages/services/src/core/types.ts`
   - `ODataApiConfig` 接口定义
   - `LegacyApiSchema` 接口定义

## 测试用例

### 用例 1：基本 OData 查询
```javascript
{
  url: "https://services.odata.org/V4/TripPinServiceRW/People",
  method: "get",
  protocol: "odata",
  query: {
    select: "FirstName,LastName"
  }
}
```

### 用例 2：带过滤的 OData 查询
```javascript
{
  url: "https://services.odata.org/V4/TripPinServiceRW/People",
  method: "get",
  protocol: "odata",
  query: {
    select: "FirstName,LastName,Gender",
    filter: "Gender eq 'Female'",
    top: 5
  }
}
```

### 用例 3：复杂 OData 查询
```javascript
{
  url: "https://services.odata.org/V4/TripPinServiceRW/People",
  method: "get",
  protocol: "odata",
  query: {
    select: "FirstName,LastName,Gender",
    filter: "Gender eq 'Male'",
    orderby: "FirstName",
    top: 10,
    skip: 5
  }
}
```

## 验证清单

- [ ] OData 查询选项正确传递到适配器
- [ ] 控制台显示正确的配置信息
- [ ] API 响应包含过滤后的数据
- [ ] 字段选择功能正常工作
- [ ] 分页功能正常工作
- [ ] 排序功能正常工作
- [ ] 错误处理正常工作

## 注意事项

1. **类型一致性**: 确保使用正确的属性名（`query` vs `odata`）
2. **向后兼容**: `LegacyApiSchema` 仍然支持 `odata` 属性
3. **配置标准化**: 配置标准化器应该将 `odata` 转换为 `query`
4. **调试信息**: 保留必要的 console.log 用于调试

## 后续改进

1. **添加单元测试**: 为 OData 配置传递添加专门的测试
2. **改进错误提示**: 当 OData 配置无效时提供更好的错误信息
3. **配置验证**: 添加 OData 查询选项的验证逻辑
4. **文档更新**: 更新相关文档说明正确的配置方式
