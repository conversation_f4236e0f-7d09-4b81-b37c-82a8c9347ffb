<template>
  <el-space direction="vertical" style="width: 100%">
    <div class="demo-section">
      <h4>基础查询演示</h4>
      <el-button-group>
        <el-button type="primary" @click="testExecuteApiGet" :loading="loading">
          executeApi (GET)
        </el-button>
        <el-button
          type="success"
          @click="testODataClientQuery"
          :loading="loading"
        >
          OData 客户端查询
        </el-button>
        <el-button type="info" @click="testPagination" :loading="loading">
          分页查询
        </el-button>
        <el-button type="warning" @click="testWithContext" :loading="loading">
          上下文变量
        </el-button>
      </el-button-group>
    </div>

    <div class="demo-section">
      <h4>CRUD 操作演示</h4>
      <el-button-group>
        <el-button
          type="primary"
          @click="testCreateWithPost"
          :loading="loading"
        >
          创建 (POST)
        </el-button>
        <el-button type="success" @click="testUpdateWithPut" :loading="loading">
          更新 (PUT)
        </el-button>
        <el-button type="info" @click="testPatchEntity" :loading="loading">
          部分更新 (PATCH)
        </el-button>
        <el-button type="danger" @click="testDeleteEntity" :loading="loading">
          删除 (DELETE)
        </el-button>
      </el-button-group>
    </div>

    <div class="demo-section">
      <h4>高级功能演示</h4>
      <el-button-group>
        <el-button type="primary" @click="testBatchRequests" :loading="loading">
          批量请求
        </el-button>
        <el-button
          type="success"
          @click="testConcurrentRequests"
          :loading="loading"
        >
          并发请求
        </el-button>
        <el-button type="info" @click="testCustomService" :loading="loading">
          自定义服务
        </el-button>
        <el-button @click="clearResults">清空结果</el-button>
      </el-button-group>
    </div>
  </el-space>
</template>
<script setup lang="ts">
import { createODataClient, executeApi, quickStart } from '@neue-plus/services'
import { ref } from 'vue'

const loading = ref(false)
const apiResult = ref<any>(null)
const error = ref('')
// 高级功能演示方法
const testBatchRequests = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 导入批量请求功能
    const { executeBatchApi } = await import('@neue-plus/services')

    // 批量执行多个请求
    const configs = [
      {
        url: 'https://services.odata.org/V4/TripPinServiceRW/People',
        method: 'get' as const,
        protocol: 'odata' as const,
        odata: {
          select: 'FirstName,LastName',
          top: 2,
          filter: 'Gender eq "Male"',
        },
      },
      {
        url: 'https://services.odata.org/V4/TripPinServiceRW/People',
        method: 'get' as const,
        protocol: 'odata' as const,
        odata: {
          select: 'FirstName,LastName',
          top: 2,
          filter: 'Gender eq "Female"',
        },
      },
    ]

    const results = await executeBatchApi(configs)

    apiResult.value = {
      data: results,
      success: true,
      total: results.length,
      message: `批量执行了 ${results.length} 个请求`,
    }
  } catch (err: any) {
    error.value = err.message || '批量请求失败'
  } finally {
    loading.value = false
  }
}

const testConcurrentRequests = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 导入并发请求功能
    const { executeConcurrentApi } = await import('@neue-plus/services')

    // 并发执行多个请求（限制并发数为2）
    const configs = [
      {
        url: 'https://services.odata.org/V4/TripPinServiceRW/People',
        method: 'get' as const,
        protocol: 'odata' as const,
        odata: { select: 'FirstName', top: 1, skip: 0 },
      },
      {
        url: 'https://services.odata.org/V4/TripPinServiceRW/People',
        method: 'get' as const,
        protocol: 'odata' as const,
        odata: { select: 'FirstName', top: 1, skip: 1 },
      },
      {
        url: 'https://services.odata.org/V4/TripPinServiceRW/People',
        method: 'get' as const,
        protocol: 'odata' as const,
        odata: { select: 'FirstName', top: 1, skip: 2 },
      },
    ]

    const results = await executeConcurrentApi(configs, {}, 2) // 最多同时2个请求

    apiResult.value = {
      data: results,
      success: true,
      total: results.length,
      message: `并发执行了 ${results.length} 个请求（并发数限制为2）`,
    }
  } catch (err: any) {
    error.value = err.message || '并发请求失败'
  } finally {
    loading.value = false
  }
}

const testCustomService = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 导入服务相关功能
    const { createApiService, createODataClient } = await import(
      '@neue-plus/services'
    )

    // 创建自定义 API 服务
    const customService = createApiService()

    // 创建自定义 OData 客户端
    const customClient = createODataClient({
      baseUrl: 'https://services.odata.org/V4/TripPinServiceRW',
      timeout: 15000,
      headers: {
        'Custom-Header': 'neue-plus-demo',
      },
    })

    // 使用自定义客户端查询
    const result = await customClient.query('People', {
      select: 'FirstName,LastName,Gender',
      top: 3,
      orderby: 'FirstName asc',
    })

    apiResult.value = {
      ...result,
      message: '使用自定义服务和客户端',
    }
  } catch (err: any) {
    error.value = err.message || '自定义服务测试失败'
  } finally {
    loading.value = false
  }
}

const testExecuteApiGet = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 使用原有的 executeApi 方法
    await quickStart()
    const result = await executeApi({
      url: 'https://services.odata.org/V4/OData/OData.svc/Products',
      method: 'get' as const,
      protocol: 'odata' as const,
      odata: {
        select: 'FirstName,LastName,Gender',
        top: 5,
        filter: 'Gender eq "Female"',
      },
      dataPath: 'data',
    })

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '请求失败'
  } finally {
    loading.value = false
  }
}

const testODataClientQuery = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 使用新的 OData 客户端
    const client = createODataClient({
      baseUrl: 'https://services.odata.org/V4/TripPinServiceRW',
      timeout: 30000,
    })

    const result = await client.query('People', {
      select: 'FirstName,LastName,Gender',
      filter: 'Gender eq "Male"',
      orderby: 'FirstName asc',
      top: 5,
      count: true,
    })

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '请求失败'
  } finally {
    loading.value = false
  }
}

const testPagination = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    const client = createODataClient({
      baseUrl: 'https://services.odata.org/V4/TripPinServiceRW',
      timeout: 30000,
    })

    const result = await client.query('People', {
      select: 'FirstName,LastName,Gender,Age',
      top: 3,
      skip: 2,
      count: true,
      orderby: 'FirstName asc',
    })

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '请求失败'
  } finally {
    loading.value = false
  }
}

const testWithContext = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    const context = {
      gender: 'Female',
      minAge: 25,
      maxResults: 4,
    }

    const result = await executeApi(
      {
        url: 'https://services.odata.org/V4/TripPinServiceRW/People',
        method: 'get' as const,
        protocol: 'odata' as const,
        odata: {
          select: 'FirstName,LastName,Gender,Age',
          filter: 'Gender eq "{{gender}}"',
          top: 4, // 直接使用数字而不是模板变量
          orderby: 'FirstName desc',
        },
        dataPath: 'data',
      },
      context
    )

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '请求失败'
  } finally {
    loading.value = false
  }
}

const clearResults = () => {
  apiResult.value = null
  error.value = ''
}

// CRUD 操作测试方法
const testCreateWithPost = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 注意：TripPin 服务是只读的，这个请求会失败，但可以演示功能
    const result = await executeApi({
      url: 'https://services.odata.org/V4/TripPinServiceRW/People',
      method: 'post' as const,
      protocol: 'odata' as const,
      body: {
        FirstName: 'John',
        LastName: 'Doe',
        Gender: 'Male',
        UserName: 'johndoe' + Date.now(),
      },
    })

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '创建失败（TripPin 服务是只读的）'
  } finally {
    loading.value = false
  }
}

const testUpdateWithPut = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 注意：TripPin 服务是只读的，这个请求会失败，但可以演示功能
    const result = await executeApi({
      url: 'https://services.odata.org/V4/TripPinServiceRW/People',
      method: 'put' as const,
      protocol: 'odata' as const,
      params: {
        id: 'russellwhyte',
      },
      body: {
        FirstName: 'Russell',
        LastName: 'Whyte',
        Gender: 'Male',
      },
    })

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '更新失败（TripPin 服务是只读的）'
  } finally {
    loading.value = false
  }
}

const testPatchEntity = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 注意：TripPin 服务是只读的，这个请求会失败，但可以演示功能
    const result = await executeApi({
      url: 'https://services.odata.org/V4/TripPinServiceRW/People',
      method: 'patch' as const,
      protocol: 'odata' as const,
      params: {
        id: 'russellwhyte',
      },
      body: {
        FirstName: 'Russell Updated',
      },
    })

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '部分更新失败（TripPin 服务是只读的）'
  } finally {
    loading.value = false
  }
}

const testDeleteEntity = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 注意：TripPin 服务是只读的，这个请求会失败，但可以演示功能
    const result = await executeApi({
      url: 'https://services.odata.org/V4/TripPinServiceRW/People',
      method: 'delete' as const,
      protocol: 'odata' as const,
      params: {
        id: 'russellwhyte',
      },
    })

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '删除失败（TripPin 服务是只读的）'
  } finally {
    loading.value = false
  }
}
</script>
