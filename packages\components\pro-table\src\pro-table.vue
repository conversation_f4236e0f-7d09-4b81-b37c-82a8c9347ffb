<!-- eslint-disable @typescript-eslint/no-unused-vars -->

<template>
  <div class="ne-pro-table">
    <div
      v-if="searchConfig !== false && filterColumns.length > 0"
      class="ne-pro-table__filter"
    >
      <ne-pro-form
        v-model="filters"
        v-bind="finalSearchConfig"
        :form-items="filterColumns"
        @submit="handleSearch"
        @reset="handleReset"
      >
        <template #extra-buttons>
          <el-button @click="handleRefresh">刷新</el-button>
        </template>
      </ne-pro-form>
    </div>

    <!-- 表格 -->
    <div class="ne-pro-table__table">
      <NeTable
        v-bind="tableProps"
        :data="dataSource"
        :columns="visibleColumns"
        :loading="loading"
        @sort-change="handleSortChange"
      >
        <slot />
      </NeTable>
    </div>

    <!-- 分页 -->
    <div v-if="paginationConfig !== false" class="ne-pro-table__pagination">
      <el-pagination
        v-bind="pagination"
        :current-page="pagination.current"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { merge, set } from 'lodash-unified'
import NeProForm from '../../pro-form'
import NeTable from '../../table'
import { type ProTableFilters, ValueTypeEnum, proTableProps } from './pro-table'

defineOptions({
  name: 'NeProTable',
  inheritAttrs: false,
})

const props = defineProps(proTableProps)
console.log(props)
// 筛选数据
const filters = ref<ProTableFilters>({})
const loading = ref(false)
const pagination = ref({ current: 1, pageSize: 10, total: 0 })
const dataSource = ref<any[]>(props.data)

const tableProps = computed(() => {
  const { data, columns, loading, bordered } = props
  return { data, columns, loading, bordered }
})

// 可见的列
const visibleColumns = computed(() => {
  return props.columns
    ?.filter((column) => !column.hideInTable)
    .map((column) => ({
      ...column,
      valueType: column.valueType ?? 'text',
    }))
})

// 最终的搜索表单配置
const finalSearchConfig = computed(() => {
  return merge(
    {
      span: 8,
      submitButtonProps: {
        type: 'primary',
      },
      resetButtonProps: {
        type: 'default',
      },
    },
    props.searchConfig
  )
})
// 可筛选的列
const filterColumns = computed(() => {
  const { span } = props.searchConfig || {}
  const result = props.columns
    .filter((column) => column.hideInForm !== true)
    .map((column) => ({
      ...column,
      span,
      valueType: column.valueType
        ? column.valueType
        : column.valueEnum
        ? ValueTypeEnum.Select
        : ValueTypeEnum.Text,
    }))
  return result
})
// 初始化筛选表单
const initFilters = () => {
  const newFilters: ProTableFilters = {}
  filterColumns.value.forEach((column) => {
    if (column.prop) {
      set(newFilters, column.prop, column.defaultValue || undefined)
    }
  })
  filters.value = newFilters
}

// 分页大小变化
const handleSizeChange = (pageSize: number) => {
  pagination.value.pageSize = pageSize
  pagination.value.current = 1
  emit('page-change', 1, pageSize)
  fetchData()
}

// 当前页变化
const handleCurrentChange = (current: number) => {
  pagination.value.current = current
  emit('page-change', current, pagination.value.pageSize)
  fetchData()
}

// 排序变化
const handleSortChange = ({
  prop,
  order,
}: {
  prop: string
  order: 'ascending' | 'descending' | null
}) => {
  // 可将排序参数存储到本地，然后 fetchData({ sortProp: prop, sortOrder: order })
  fetchData({ sortProp: prop, sortOrder: order })
  emit('sort-change', prop, order)
}

// 监听columns变化，重新初始化筛选表单
watch(
  () => props.columns,
  () => {
    initFilters()
  },
  { immediate: true }
)
watch(
  () => props.data,
  (val) => {
    dataSource.value = val
  }
)
async function fetchData(extraParams = {}) {
  if (typeof props.request === 'function') {
    loading.value = true
    try {
      const params = {
        ...pagination.value,
        ...filters.value,
        ...extraParams,
        // 可扩展排序参数
      }
      const { data, total } = await props.request(params)
      dataSource.value = data
      pagination.value.total = total || data.length
    } finally {
      loading.value = false
    }
  } else {
    dataSource.value = props.data
  }
}
const emit = defineEmits([
  'filter-change',
  'page-change',
  'sort-change',
  'refresh',
])

// 搜索
const handleSearch = () => {
  emit('filter-change', filters.value)
  fetchData()
}

// 重置筛选
const handleReset = () => {
  initFilters()
  emit('filter-change', filters.value)
  fetchData()
}

// 刷新
const handleRefresh = () => {
  emit('refresh')
  fetchData()
}
onMounted(() => {
  fetchData()
})
</script>
