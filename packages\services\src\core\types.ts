/**
 * 核心类型定义
 */

// 支持的协议类型
export type Protocol = 'rest' | 'odata'

// HTTP 方法类型
export type HttpMethod = 'get' | 'post' | 'put' | 'patch' | 'delete'

// 基础请求配置
export interface BaseRequestConfig {
  url: string
  method: HttpMethod
  headers?: Record<string, string>
  timeout?: number
}

// REST API 配置
export interface RestApiConfig extends BaseRequestConfig {
  protocol?: 'rest'
  params?: Record<string, any>
  body?: any
  dataPath?: string
}

// OData 查询选项
export interface ODataQueryOptions {
  filter?: string
  orderby?: string
  select?: string
  expand?: string
  top?: number
  skip?: number
  count?: boolean
  search?: string
  apply?: string
  custom?: Record<string, any>
}

// OData API 配置
export interface ODataApiConfig extends BaseRequestConfig {
  protocol: 'odata'
  params?: Record<string, any>
  body?: any
  dataPath?: string
  query?: ODataQueryOptions
}

// 统一的 API 配置（向后兼容）
export interface LegacyApiSchema {
  url: string
  method: HttpMethod
  protocol?: Protocol
  headers?: Record<string, any>
  params?: Record<string, any>
  body?: any
  dataPath?: string
  map?: { label: string; value: string }
  odata?: ODataQueryOptions
  timeout?: number
}

// 统一的 API 配置
export type ApiConfig = RestApiConfig | ODataApiConfig | LegacyApiSchema

// 请求上下文
export interface RequestContext {
  [key: string]: any
}

// 统一的响应格式
export interface ApiResponse<T = any> {
  data: T | T[]
  success: boolean
  total?: number
  page?: number
  pageSize?: number
  hasNext?: boolean
  hasPrev?: boolean
  error?: string
  // 缓存相关字段
  fromCache?: boolean
  stale?: boolean
  cacheKey?: string
}

// OData 实体基类
export interface ODataEntity {
  [key: string]: any
}

// OData 服务响应
export interface ODataServiceResponse<T = any> {
  '@odata.context'?: string
  '@odata.count'?: number
  '@odata.nextLink'?: string
  value: T[]
}

// OData 客户端配置
export interface ODataClientConfig {
  baseUrl: string
  entitySet?: string
  headers?: Record<string, string>
  timeout?: number
  auth?: AuthConfig
}

// 认证配置
export interface AuthConfig {
  type: 'basic' | 'bearer' | 'oauth2'
  credentials?: {
    username?: string
    password?: string
    token?: string
    clientId?: string
    clientSecret?: string
  }
}

// 错误处理器类型
export type ErrorHandler = (message: string) => void

// 表达式解析器接口
export interface ExpressionParser {
  parse(template: any, context: RequestContext): any
}

// 适配器接口
export interface ApiAdapter {
  canHandle(config: ApiConfig): boolean
  execute(config: ApiConfig, context: RequestContext): Promise<ApiResponse>
}

// 客户端接口
export interface ApiClient {
  request<T = any>(config: BaseRequestConfig): Promise<T>
}

// OData 客户端接口
export interface IODataClient extends ApiClient {
  query<T = ODataEntity>(
    entitySet?: string,
    options?: ODataQueryOptions,
    context?: RequestContext
  ): Promise<ApiResponse<T>>

  get<T = ODataEntity>(
    key: string | number,
    entitySet?: string,
    options?: Pick<ODataQueryOptions, 'select' | 'expand'>,
    context?: RequestContext
  ): Promise<ApiResponse<T>>

  create<T = ODataEntity>(
    entity: Partial<T>,
    entitySet?: string
  ): Promise<ApiResponse<T>>

  update<T = ODataEntity>(
    key: string | number,
    entity: Partial<T>,
    entitySet?: string
  ): Promise<ApiResponse<T>>

  patch<T = ODataEntity>(
    key: string | number,
    entity: Partial<T>,
    entitySet?: string
  ): Promise<ApiResponse<T>>

  delete(
    key: string | number,
    entitySet?: string
  ): Promise<ApiResponse<boolean>>

  callFunction<T = any>(
    functionName: string,
    parameters?: Record<string, any>,
    context?: RequestContext
  ): Promise<T | null>

  callAction<T = any>(
    actionName: string,
    parameters?: Record<string, any>,
    context?: RequestContext
  ): Promise<T | null>
}
