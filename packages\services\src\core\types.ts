/**
 * 核心类型定义
 */

import { ODataKeyPredicate } from '@odata/client'

// 支持的协议类型
export type Protocol = 'rest' | 'odata'

// HTTP 方法类型
export type HttpMethod = 'get' | 'post' | 'put' | 'patch' | 'delete'

// 基础请求配置
export interface BaseRequestConfig {
  url: string
  method: HttpMethod
  headers?: Record<string, string>
  timeout?: number
}

// REST API 配置
export interface RestApiConfig extends BaseRequestConfig {
  protocol?: 'rest'
  params?: Record<string, any>
  body?: any
  dataPath?: string
}

// OData 查询选项
export interface ODataQueryOptions {
  /**
   * 筛选记录（类似 SQL 的 WHERE 子句）
   * 示例: Age gt 20
   */
  filter?: string

  /**
   * 选择返回的字段（类似 SQL 的 SELECT 子句）
   * 示例: Name,Age
   */
  select?: string

  /**
   * 排序字段及方向（asc 或 desc）
   * 示例: CreatedDate desc
   */
  orderby?: string

  /**
   * 限制返回的记录数（分页用）
   * 示例: 5
   */
  top?: number

  /**
   * 跳过前 N 条记录（分页用）
   * 示例: 10
   */
  skip?: number

  /**
   * 展开导航属性（关联查询）
   * 示例: Orders
   */
  expand?: any[]

  /**
   * 是否返回总记录数（通常用于分页）
   * 示例: true
   */
  count?: boolean

  /**
   * 指定响应的格式（一般不需要设置）
   * 示例: json
   */
  format?: 'json' | 'xml' | string

  /**
   * 全文搜索（只有部分服务支持，例如 Azure Search）
   * 示例: bike
   */
  search?: string

  /**
   * 计算新字段（OData v4 支持）
   * 示例: Price mul Quantity as Total
   */
  inlinecount?: string
  apply?: string
}

// OData API 配置
export interface ODataApiConfig extends BaseRequestConfig {
  protocol: 'odata'
  params?: Record<string, any>
  body?: any
  dataPath?: string
  query?: ODataQueryOptions
}

// 统一的 API 配置（向后兼容）
export interface LegacyApiSchema {
  url: string
  method: HttpMethod
  protocol?: Protocol
  headers?: Record<string, any>
  params?: Record<string, any>
  body?: any
  dataPath?: string
  map?: { label: string; value: string }
  odata?: ODataQueryOptions
  timeout?: number
}

// 统一的 API 配置
export type ApiConfig = RestApiConfig | ODataApiConfig | LegacyApiSchema

// 请求上下文
export interface RequestContext {
  [key: string]: any
}

// 统一的响应格式
export interface ApiResponse<T = any> {
  data: T | T[]
  success: boolean
  total?: number
  page?: number
  pageSize?: number
  hasNext?: boolean
  hasPrev?: boolean
  error?: string
  // 缓存相关字段
  fromCache?: boolean
  stale?: boolean
  cacheKey?: string
}

// OData 实体基类
export interface ODataEntity {
  [key: string]: any
}

// OData 服务响应
export interface ODataServiceResponse<T = any> {
  '@odata.context'?: string
  '@odata.count'?: number
  '@odata.nextLink'?: string
  value: T[]
}

// OData 客户端配置
export interface ODataClientConfig {
  baseUrl: string
  entitySet?: string
  headers?: Record<string, string>
  timeout?: number
  auth?: AuthConfig
}

// 认证配置
export interface AuthConfig {
  type: 'basic' | 'bearer' | 'oauth2'
  credentials?: {
    username?: string
    password?: string
    token?: string
    clientId?: string
    clientSecret?: string
  }
}

// 错误处理器类型
export type ErrorHandler = (message: string) => void

// 表达式解析器接口
export interface ExpressionParser {
  parse(template: any, context: RequestContext): any
}

// 适配器接口
export interface ApiAdapter {
  canHandle(config: ApiConfig): boolean
  execute(config: ApiConfig, context: RequestContext): Promise<ApiResponse>
}

// 客户端接口
export interface ApiClient {
  request<T = any>(config: BaseRequestConfig): Promise<T>
}

// OData 客户端接口
export interface IODataClient extends ApiClient {
  /**
   * 查询实体集合（即 entitySet）
   * @param entitySet 实体集名称（例如：'People'，相当于数据库中的表）
   * @param options 查询选项，如 $filter、$top、$select 等
   * @param context 可选的请求上下文信息
   */
  query<T = ODataEntity>(
    entitySet: string,
    options?: ODataQueryOptions,
    entity?: Partial<T>
  ): Promise<ApiResponse<T>>

  get<T = ODataEntity>(
    entitySet: string,
    key: ODataKeyPredicate
    // options?: Pick<ODataQueryOptions, 'select' | 'expand'>,
    // context?: RequestContext
  ): Promise<ApiResponse<T>>

  create<T = ODataEntity>(
    entitySet: string,
    entity?: Partial<T>
  ): Promise<ApiResponse<T>>

  update<T = ODataEntity>(
    entitySet: string,
    key: ODataKeyPredicate,
    entity?: Partial<T>
  ): Promise<ApiResponse<T>>

  patch<T = ODataEntity>(
    entitySet: string,
    key: ODataKeyPredicate,
    entity?: Partial<T>
  ): Promise<ApiResponse<T>>

  delete(
    entitySet: string,
    key: ODataKeyPredicate
  ): Promise<ApiResponse<boolean>>

  callFunction<T = any>(
    functionName: string,
    parameters?: Record<string, any>,
    context?: RequestContext
  ): Promise<T | null>

  callAction<T = any>(
    actionName: string,
    parameters?: Record<string, any>,
    context?: RequestContext
  ): Promise<T | null>
}
