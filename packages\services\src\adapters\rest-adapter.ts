/**
 * REST API 适配器
 * 处理标准的 REST API 请求
 */

import { HttpClient, getDefaultHttpClient } from '../clients/http-client'
import { parseExpression } from '../core/expression-parser'
import type { ProtocolAdapter } from '../core/interfaces'
import type {
  ApiConfig,
  ApiResponse,
  RequestContext,
  RestApiConfig,
} from '../core/types'

export class RestAdapter implements ProtocolAdapter<ApiConfig> {
  readonly protocol = 'rest'
  private httpClient: HttpClient

  constructor(httpClient?: HttpClient) {
    this.httpClient = httpClient || getDefaultHttpClient()
  }

  /**
   * 检查是否支持该配置
   */
  supports(config: ApiConfig): boolean {
    return !config.protocol || config.protocol === 'rest'
  }

  /**
   * 执行 REST API 请求
   */
  async execute(
    config: ApiConfig,
    context: RequestContext = {}
  ): Promise<ApiResponse> {
    const restConfig = config as RestApiConfig

    try {
      // 解析 URL 中的表达式
      const url = parseExpression(restConfig.url, context)

      // 解析参数中的表达式
      const params = restConfig.params
        ? parseExpression(restConfig.params, context)
        : undefined

      // 解析请求体中的表达式
      const body = restConfig.body
        ? parseExpression(restConfig.body, context)
        : undefined

      // 解析头部中的表达式
      const headers = restConfig.headers
        ? parseExpression(restConfig.headers, context)
        : undefined

      // 发送请求
      const response = await this.httpClient.request({
        url,
        method: restConfig.method,
        headers,
        timeout: restConfig.timeout,
        params,
        body,
      } as any)

      // 提取数据
      let data = response
      if (restConfig.dataPath) {
        data = this.extractDataByPath(response, restConfig.dataPath)
      }

      return {
        data,
        success: true,
        total: Array.isArray(data) ? data.length : data ? 1 : 0,
        page: 1,
        pageSize: Array.isArray(data) ? data.length : data ? 1 : 0,
        hasNext: false,
        hasPrev: false,
      }
    } catch (error) {
      return {
        data: null,
        success: false,
        total: 0,
        page: 1,
        pageSize: 0,
        hasNext: false,
        hasPrev: false,
        error: error instanceof Error ? error.message : 'Request failed',
      }
    }
  }

  /**
   * 根据路径提取数据
   */
  private extractDataByPath(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined
    }, obj)
  }
}

// 默认 REST 适配器实例
let defaultRestAdapter: RestAdapter | null = null

/**
 * 获取默认 REST 适配器实例
 */
export function getDefaultRestAdapter(): RestAdapter {
  if (!defaultRestAdapter) {
    defaultRestAdapter = new RestAdapter()
  }
  return defaultRestAdapter
}

/**
 * 设置默认 REST 适配器实例
 */
export function setDefaultRestAdapter(adapter: RestAdapter): void {
  defaultRestAdapter = adapter
}

/**
 * 创建 REST 适配器实例
 */
export function createRestAdapter(httpClient?: HttpClient): RestAdapter {
  return new RestAdapter(httpClient)
}
