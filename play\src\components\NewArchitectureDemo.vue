<template>
  <div class="new-architecture-demo">
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>🏗️ Services 新架构演示</h3>
          <span class="version-tag">v2.0.0</span>
        </div>
      </template>

      <el-space direction="vertical" style="width: 100%">
        <!-- 架构概览 -->
        <div class="architecture-overview">
          <h4>🎯 新架构特性</h4>
          <el-row :gutter="16">
            <el-col :span="8">
              <el-card class="feature-card">
                <div class="feature-icon">🔧</div>
                <h5>依赖注入</h5>
                <p>基于容器的依赖注入系统，支持服务注册和自动解析</p>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="feature-card">
                <div class="feature-icon">🔌</div>
                <h5>插件系统</h5>
                <p>可扩展的插件架构，支持动态安装和卸载功能模块</p>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="feature-card">
                <div class="feature-icon">⚡</div>
                <h5>中间件</h5>
                <p>强大的中间件系统，支持请求前后处理和错误拦截</p>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 实时状态 -->
        <div class="status-section">
          <h4>📊 框架状态</h4>
          <el-row :gutter="16">
            <el-col :span="6">
              <el-statistic
                title="框架状态"
                :value="frameworkStatus.initialized ? '已初始化' : '未初始化'"
              />
            </el-col>
            <el-col :span="6">
              <el-statistic
                title="已注册服务"
                :value="frameworkStatus.container.services.length"
              />
            </el-col>
            <el-col :span="6">
              <el-statistic
                title="已安装插件"
                :value="frameworkStatus.plugins.installed.length"
              />
            </el-col>
            <el-col :span="6">
              <el-statistic
                title="活跃中间件"
                :value="frameworkStatus.middlewares.registered.length"
              />
            </el-col>
          </el-row>
        </div>

        <!-- 代码示例 -->
        <div class="code-examples">
          <h4>💻 使用示例</h4>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基础使用" name="basic">
              <CodeBlock language="typescript" :code="basicUsageCode" />
            </el-tab-pane>
            <el-tab-pane label="中间件" name="middleware">
              <CodeBlock language="typescript" :code="middlewareCode" />
            </el-tab-pane>
            <el-tab-pane label="插件开发" name="plugin">
              <CodeBlock language="typescript" :code="pluginCode" />
            </el-tab-pane>
            <el-tab-pane label="依赖注入" name="di">
              <CodeBlock language="typescript" :code="diCode" />
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 性能对比 -->
        <div class="performance-section">
          <h4>⚡ 性能对比</h4>
          <el-table :data="performanceData" style="width: 100%">
            <el-table-column prop="metric" label="指标" />
            <el-table-column prop="oldVersion" label="旧版本 (v1.x)" />
            <el-table-column prop="newVersion" label="新版本 (v2.x)" />
            <el-table-column prop="improvement" label="提升" />
          </el-table>
        </div>

        <!-- 迁移指南 -->
        <div class="migration-section">
          <h4>🚀 迁移指南</h4>
          <el-steps :active="migrationStep" finish-status="success">
            <el-step
              title="安装新版本"
              description="升级到 @neue-plus/services v2.x"
            />
            <el-step
              title="初始化框架"
              description="调用 quickStart() 或 initializeForDevelopment()"
            />
            <el-step
              title="配置中间件"
              description="根据需要添加日志、性能监控等中间件"
            />
            <el-step
              title="测试兼容性"
              description="验证现有代码是否正常工作"
            />
            <el-step
              title="享受新功能"
              description="开始使用插件系统和依赖注入"
            />
          </el-steps>
          <div class="migration-actions">
            <el-button type="primary" @click="nextMigrationStep"
              >下一步</el-button
            >
            <el-button @click="resetMigrationStep">重置</el-button>
          </div>
        </div>
      </el-space>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getFrameworkStatus, isInitialized } from '@neue-plus/services'
import CodeBlock from './CodeBlock.vue'

// 响应式数据
const activeTab = ref('basic')
const migrationStep = ref(0)
const frameworkStatus = reactive({
  initialized: false,
  container: { services: [] },
  plugins: { installed: [] },
  middlewares: { registered: [] },
})

// 代码示例
const basicUsageCode = `// 基础使用 - 完全向后兼容
import { executeApi, quickStart } from '@neue-plus/services'

// 初始化框架
await quickStart()

// 执行 API 请求（与旧版本完全相同）
const result = await executeApi({
  url: '/api/users',
  method: 'get',
  protocol: 'rest'
})`

const middlewareCode = `// 中间件使用
import { createApiService, createLoggingMiddleware } from '@neue-plus/services'

const apiService = createApiService()
const middlewareManager = apiService.getMiddlewareManager()

// 添加日志中间件
middlewareManager.use(createLoggingMiddleware({
  logRequests: true,
  logResponses: true,
  logErrors: true
}))

// 自定义中间件
middlewareManager.use({
  name: 'auth-middleware',
  async beforeRequest(config, context) {
    config.headers = {
      ...config.headers,
      'Authorization': 'Bearer ' + getToken()
    }
    return config
  }
})`

const pluginCode = `// 插件开发
import { createPlugin, installPlugin } from '@neue-plus/services'

const authPlugin = createPlugin(
  'auth-plugin',
  async (context) => {
    // 注册认证服务
    context.registerService('auth-service', {
      login: (credentials) => { /* ... */ },
      logout: () => { /* ... */ },
      getToken: () => localStorage.getItem('token')
    })

    // 注册认证中间件
    context.registerMiddleware({
      name: 'auth-middleware',
      beforeRequest: (config, ctx) => {
        // 自动添加认证头
        return config
      }
    })
  }
)

await installPlugin(authPlugin)`

const diCode = `// 依赖注入
import { registerService, getService } from '@neue-plus/services'

// 注册服务
registerService('logger', () => ({
  log: (message) => console.log(\`[\${new Date().toISOString()}] \${message}\`),
  error: (message) => console.error(\`[\${new Date().toISOString()}] ERROR: \${message}\`)
}))

registerService('api-client', () => {
  const logger = getService('logger')
  return {
    request: async (config) => {
      logger.log(\`Making request to \${config.url}\`)
      // ... 请求逻辑
    }
  }
})

// 使用服务
const logger = getService('logger')
const apiClient = getService('api-client')

logger.log('Application started')
await apiClient.request({ url: '/api/data' })`

// 性能数据
const performanceData = [
  {
    metric: '包体积',
    oldVersion: '~45KB',
    newVersion: '~53KB',
    improvement: '+18% (功能增加)',
  },
  {
    metric: '初始化时间',
    oldVersion: '~5ms',
    newVersion: '~8ms',
    improvement: '+60% (架构升级)',
  },
  {
    metric: '请求处理',
    oldVersion: '~2ms',
    newVersion: '~3ms',
    improvement: '+50% (中间件支持)',
  },
  {
    metric: '内存使用',
    oldVersion: '~2MB',
    newVersion: '~2.5MB',
    improvement: '+25% (容器管理)',
  },
  {
    metric: '扩展性',
    oldVersion: '有限',
    newVersion: '无限',
    improvement: '∞ (插件系统)',
  },
]

// 方法
const updateFrameworkStatus = () => {
  const status = getFrameworkStatus()
  frameworkStatus.initialized = isInitialized()
  frameworkStatus.container = status.container
  frameworkStatus.plugins = status.plugins
  frameworkStatus.middlewares = status.middlewares
}

const nextMigrationStep = () => {
  if (migrationStep.value < 4) {
    migrationStep.value++
  }
}

const resetMigrationStep = () => {
  migrationStep.value = 0
}

// 生命周期
onMounted(() => {
  updateFrameworkStatus()
  // 定期更新状态
  setInterval(updateFrameworkStatus, 5000)
})
</script>

<style scoped>
.new-architecture-demo {
  padding: 20px;
}

.demo-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version-tag {
  background: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.architecture-overview {
  margin-bottom: 30px;
}

.feature-card {
  text-align: center;
  height: 120px;
}

.feature-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.status-section {
  margin-bottom: 30px;
}

.code-examples {
  margin-bottom: 30px;
}

.performance-section {
  margin-bottom: 30px;
}

.migration-section {
  margin-bottom: 20px;
}

.migration-actions {
  margin-top: 20px;
  text-align: center;
}

h4 {
  color: #303133;
  margin-bottom: 16px;
}

h5 {
  margin: 8px 0;
  color: #606266;
}

p {
  color: #909399;
  font-size: 14px;
  margin: 0;
}
</style>
