<template>
  <div class="code-block">
    <div class="code-header">
      <span class="language">{{ language }}</span>
      <el-button 
        size="small" 
        type="text" 
        @click="copyCode"
        :icon="copied ? 'Check' : 'DocumentCopy'"
      >
        {{ copied ? '已复制' : '复制' }}
      </el-button>
    </div>
    <pre class="code-content"><code>{{ code }}</code></pre>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  code: string
  language?: string
}

const props = withDefaults(defineProps<Props>(), {
  language: 'javascript'
})

const copied = ref(false)

const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(props.code)
    copied.value = true
    setTimeout(() => {
      copied.value = false
    }, 2000)
  } catch (err) {
    console.error('Failed to copy code:', err)
  }
}
</script>

<style scoped>
.code-block {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
  background-color: #fafafa;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.language {
  font-size: 12px;
  color: #909399;
  text-transform: uppercase;
  font-weight: 600;
}

.code-content {
  margin: 0;
  padding: 16px;
  background-color: #ffffff;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #2c3e50;
  overflow-x: auto;
}

.code-content code {
  background: none;
  padding: 0;
  font-size: inherit;
  color: inherit;
}
</style>
