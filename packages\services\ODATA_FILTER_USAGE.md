# 🔍 OData 过滤器使用指南

## 概述

我们为 OData 查询提供了强大的过滤器支持，包括：
- 结构化过滤规则 `ODataFilterRule[]`
- 过滤器构建工具 `ODataFilterBuilder`
- 链式查询构建器 `ODataQueryBuilder`

## 🎯 类型定义

### **FilterOperator**
```typescript
type FilterOperator = 'eq' | 'ne' | 'contains' | 'startswith' | 'endswith' | 'gt' | 'ge' | 'lt' | 'le'
```

### **ODataFilterRule**
```typescript
interface ODataFilterRule {
  field: string                           // 字段名
  value: string | number | boolean        // 值
  operator?: FilterOperator               // 操作符，默认 'eq'
  logical?: 'and' | 'or'                 // 逻辑操作符
}
```

## 🚀 使用方式

### **1. 直接使用结构化过滤器**

#### **在 ApiRequestButton 中**
```typescript
// 你当前的用法
const config = {
  url: 'https://services.odata.org/V4/TripPinServiceRW/People',
  method: 'get',
  protocol: 'odata',
  query: {
    select: 'FirstName,LastName,Gender',
    top: 5,
    filter: [
      { field: 'FirstName', value: 'John', operator: 'contains' },
      { field: 'LastName', value: 'Doe', operator: 'contains', logical: 'or' },
    ]
  }
}

const result = await executeApi(config)
```

#### **生成的 OData 查询**
```
$filter=contains(FirstName, 'John') or contains(LastName, 'Doe')
$select=FirstName,LastName,Gender
$top=5
```

### **2. 使用过滤器构建工具**

#### **基础用法**
```typescript
import { filter } from '@neue-plus/services'

// 构建过滤规则
const filterRules = [
  filter.contains('FirstName', 'John'),
  filter.or('LastName', 'Doe', 'contains'),
  filter.and('Age', 18, 'gt'),
  filter.and('IsActive', true)
]

// 在查询中使用
const config = {
  url: 'https://services.odata.org/V4/TripPinServiceRW/People',
  method: 'get',
  protocol: 'odata',
  query: {
    filter: filterRules,
    select: 'FirstName,LastName,Age',
    top: 10
  }
}
```

#### **所有支持的方法**
```typescript
// 等于
filter.eq('Name', 'John')                    // Name eq 'John'

// 不等于
filter.ne('Status', 'Inactive')              // Status ne 'Inactive'

// 包含
filter.contains('Email', 'gmail')            // contains(Email, 'gmail')

// 开始于
filter.startsWith('Name', 'J')               // startswith(Name, 'J')

// 结束于
filter.endsWith('Email', '.com')             // endswith(Email, '.com')

// 数值比较
filter.gt('Age', 18)                         // Age gt 18
filter.ge('Age', 18)                         // Age ge 18
filter.lt('Age', 65)                         // Age lt 65
filter.le('Age', 65)                         // Age le 65

// 逻辑操作
filter.and('IsActive', true)                 // and IsActive eq true
filter.or('Department', 'IT', 'eq')          // or Department eq 'IT'
```

### **3. 使用链式查询构建器**

#### **完整的查询构建**
```typescript
import { query } from '@neue-plus/services'

const options = query()
  .select(['FirstName', 'LastName', 'Email', 'Age'])
  .contains('FirstName', 'John')
  .or('LastName', 'Doe', 'contains')
  .and('Age', 18, 'gt')
  .and('IsActive', true)
  .orderBy('FirstName', 'asc')
  .orderBy('Age', 'desc')
  .top(20)
  .skip(10)
  .count(true)
  .build()

// 使用构建的选项
const result = await executeApi({
  url: 'https://services.odata.org/V4/TripPinServiceRW/People',
  method: 'get',
  protocol: 'odata',
  query: options
})
```

#### **链式构建器的所有方法**
```typescript
const builder = query()
  // 过滤方法
  .eq('field', 'value')           // 等于
  .ne('field', 'value')           // 不等于
  .contains('field', 'value')     // 包含
  .startsWith('field', 'value')   // 开始于
  .endsWith('field', 'value')     // 结束于
  .gt('field', 100)               // 大于
  .ge('field', 100)               // 大于等于
  .lt('field', 100)               // 小于
  .le('field', 100)               // 小于等于
  .and('field', 'value')          // AND 条件
  .or('field', 'value')           // OR 条件
  
  // 查询选项
  .select('field1,field2')        // 选择字段
  .select(['field1', 'field2'])   // 选择字段（数组）
  .expand('RelatedEntity')        // 展开关联
  .orderBy('field', 'asc')        // 排序
  .top(10)                        // 限制数量
  .skip(5)                        // 跳过数量
  .count(true)                    // 启用计数
  .search('keyword')              // 搜索
  
  // 构建和重置
  .build()                        // 构建查询选项
  .reset()                        // 重置构建器
  .clone()                        // 克隆构建器
```

## 📊 实际应用示例

### **示例1：用户搜索**
```typescript
// 搜索名字包含 "John" 或邮箱包含 "john" 的用户
const userSearch = query()
  .select('FirstName,LastName,Email')
  .contains('FirstName', 'John')
  .or('Email', 'john', 'contains')
  .orderBy('FirstName')
  .top(10)
  .build()

const users = await executeApi({
  url: 'https://services.odata.org/V4/TripPinServiceRW/People',
  method: 'get',
  protocol: 'odata',
  query: userSearch
})
```

### **示例2：复杂条件查询**
```typescript
// 查找活跃的成年用户，按年龄排序
const activeAdults = query()
  .select('FirstName,LastName,Age,IsActive')
  .eq('IsActive', true)
  .and('Age', 18, 'ge')
  .and('Age', 65, 'lt')
  .orderBy('Age', 'desc')
  .top(50)
  .count(true)
  .build()

const result = await executeApi({
  url: 'https://api.example.com/odata/Users',
  method: 'get',
  protocol: 'odata',
  query: activeAdults
})
```

### **示例3：分页查询**
```typescript
// 分页查询，每页20条，获取第2页
const pageQuery = query()
  .select('FirstName,LastName,Email')
  .orderBy('FirstName')
  .top(20)
  .skip(20)  // 跳过第一页的20条
  .count(true)
  .build()

const page2 = await executeApi({
  url: 'https://services.odata.org/V4/TripPinServiceRW/People',
  method: 'get',
  protocol: 'odata',
  query: pageQuery
})
```

### **示例4：多条件搜索**
```typescript
// 搜索IT部门的高级员工
const seniorITStaff = query()
  .select('FirstName,LastName,Department,Level,Salary')
  .eq('Department', 'IT')
  .and('Level', 'Senior', 'contains')
  .and('Salary', 50000, 'gt')
  .orderBy('Salary', 'desc')
  .top(10)
  .build()
```

## 🔄 生成的 OData 查询示例

### **输入**
```typescript
const rules = [
  { field: 'FirstName', value: 'John', operator: 'contains' },
  { field: 'LastName', value: 'Doe', operator: 'contains', logical: 'or' },
  { field: 'Age', value: 18, operator: 'gt', logical: 'and' },
  { field: 'IsActive', value: true, logical: 'and' }
]
```

### **输出**
```
$filter=contains(FirstName, 'John') or contains(LastName, 'Doe') and Age gt 18 and IsActive eq true
```

## ✅ 优势

### **1. 类型安全**
- ✅ 完整的 TypeScript 类型支持
- ✅ 编译时错误检查
- ✅ 智能代码提示

### **2. 易于使用**
- ✅ 直观的结构化配置
- ✅ 链式调用API
- ✅ 丰富的便捷方法

### **3. 功能完整**
- ✅ 支持所有常用的 OData 操作符
- ✅ 支持复杂的逻辑组合
- ✅ 支持完整的查询选项

### **4. 向后兼容**
- ✅ 仍然支持字符串格式的过滤器
- ✅ 不破坏现有代码
- ✅ 渐进式升级

现在你可以在 ApiRequestButton 中使用更强大和类型安全的 OData 过滤功能了！
