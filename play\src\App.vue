<template>
  <div>
    <div class="mt-4">
      <h3>NeProTable</h3>
      <NeProTable
        :columns="proTableColumns"
        :data="allProTableData"
        :searchConfig="searchConfig"
        :request="request"
      />
    </div>
    <div class="mt-4">
      <h3>MaterialRender apis 测试</h3>
      <NeMaterialRender v-bind="odataMaterialConfig" />
    </div>
    <div>
      <h3>basic-form测试</h3>
      <NeCard>
        <NeMaterialRender v-bind="basicFormConfig" />
      </NeCard>
    </div>

    <div class="mt-4">
      <h3>OData Services Demo</h3>
      <NeCard>
        <template #header>
          <div class="card-header">
            <span>OData 客户端功能演示</span>
          </div>
        </template>

        <el-space direction="vertical" style="width: 100%">
          <div class="demo-section">
            <h4>基础查询演示</h4>
            <el-button-group>
              <el-button
                type="primary"
                @click="testExecuteApiGet"
                :loading="loading"
              >
                executeApi (GET)
              </el-button>
              <el-button
                type="success"
                @click="testODataClientQuery"
                :loading="loading"
              >
                OData 客户端查询
              </el-button>
              <el-button type="info" @click="testPagination" :loading="loading">
                分页查询
              </el-button>
              <el-button
                type="warning"
                @click="testWithContext"
                :loading="loading"
              >
                上下文变量
              </el-button>
            </el-button-group>
          </div>

          <div class="demo-section">
            <h4>CRUD 操作演示</h4>
            <el-button-group>
              <el-button
                type="primary"
                @click="testCreateWithPost"
                :loading="loading"
              >
                创建 (POST)
              </el-button>
              <el-button
                type="success"
                @click="testUpdateWithPut"
                :loading="loading"
              >
                更新 (PUT)
              </el-button>
              <el-button
                type="info"
                @click="testPatchEntity"
                :loading="loading"
              >
                部分更新 (PATCH)
              </el-button>
              <el-button
                type="danger"
                @click="testDeleteEntity"
                :loading="loading"
              >
                删除 (DELETE)
              </el-button>
            </el-button-group>
          </div>

          <div class="demo-section">
            <h4>高级功能演示</h4>
            <el-button-group>
              <el-button
                type="primary"
                @click="testBatchRequests"
                :loading="loading"
              >
                批量请求
              </el-button>
              <el-button
                type="success"
                @click="testConcurrentRequests"
                :loading="loading"
              >
                并发请求
              </el-button>
              <el-button
                type="info"
                @click="testCustomService"
                :loading="loading"
              >
                自定义服务
              </el-button>
              <el-button @click="clearResults">清空结果</el-button>
            </el-button-group>
          </div>

          <div class="demo-section">
            <h4>缓存和去重演示</h4>
            <el-button-group>
              <el-button
                type="primary"
                @click="testCacheFirst"
                :loading="loading"
              >
                缓存优先策略
              </el-button>
              <el-button
                type="success"
                @click="testNetworkFirst"
                :loading="loading"
              >
                网络优先策略
              </el-button>
              <el-button
                type="info"
                @click="testRequestDedupe"
                :loading="loading"
              >
                请求去重
              </el-button>
              <el-button
                type="warning"
                @click="testCacheStats"
                :loading="loading"
              >
                缓存统计
              </el-button>
              <el-button type="danger" @click="clearCache">
                清空缓存
              </el-button>
            </el-button-group>
          </div>

          <el-divider />

          <div v-if="apiResult && !loading">
            <h4>API 结果:</h4>
            <el-card class="result-card">
              <div class="result-info">
                <el-tag v-if="apiResult.success" type="success">成功</el-tag>
                <el-tag v-else type="danger">失败</el-tag>
                <span v-if="apiResult.total !== undefined" class="ml-2">
                  总计: {{ apiResult.total }} 条
                </span>
                <span v-if="apiResult.page !== undefined" class="ml-2">
                  第 {{ apiResult.page }} 页
                </span>
                <span v-if="apiResult.pageSize !== undefined" class="ml-2">
                  每页: {{ apiResult.pageSize }} 条
                </span>
              </div>
              <el-divider />
              <div class="result-data">
                <el-collapse v-model="activeCollapse">
                  <el-collapse-item title="查看详细数据" name="1">
                    <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </el-card>
          </div>

          <div v-if="error" class="error">
            <el-alert
              :title="error"
              type="error"
              show-icon
              :closable="true"
              @close="error = ''"
            />
          </div>
        </el-space>
      </NeCard>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, provide } from 'vue'
import {
  type ProTableColumn,
  type ProTableFilters,
} from '@neue-plus/components'
import {
  executeApi,
  createODataClient,
  createHttpClient,
  ApiService,
  createApiService,
  getGlobalCacheManager,
  getGlobalStrategyManager,
  createCacheManager,
} from '@neue-plus/services'
import { pageData, odataMaterialConfig, basicFormConfig } from './constant'

let searchConfig = ref({
  span: 8,
})
// 创建 refs 管理
const refs = ref({})
provide('refs', refs.value)

// OData 演示相关状态
const loading = ref(false)
const apiResult = ref<any>(null)
const error = ref('')
const activeCollapse = ref(['1'])

const tableData = ref([
  {
    name: '张三',
    age: 25,
    address: '北京市朝阳区',
  },
  {
    name: '李四',
    age: 30,
    address: '上海市浦东新区',
  },
  {
    name: '王五',
    age: 28,
    address: '广州市天河区',
  },
  {
    name: '赵六',
    age: 32,
    address: '深圳市南山区',
  },
])

// 表格列配置 - 继承el-table-column的所有属性
const tableColumns = ref([
  {
    prop: 'FirstName',
    label: '姓名',
    fixed: 'left',
  },
  {
    prop: 'age',
    label: '年龄',
    sortable: true,
  },
  {
    prop: 'LastName',
    label: '地址',
  },
])

// ProTable相关数据
// const loading = ref(false) // 已在上面定义
const filters = ref<ProTableFilters>({})
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
})

// 扩展数据用于ProTable测试
const allProTableData = ref([
  {
    name: '张三',
    age: 25,
    address: '北京市朝阳区',
    status: 'active',
    createTime: '2024-01-01',
  },
  {
    name: '李四',
    age: 30,
    address: '上海市浦东新区',
    status: 'inactive',
    createTime: '2024-01-02',
  },
  {
    name: '王五',
    age: 28,
    address: '广州市天河区',
    status: 'active',
    createTime: '2024-01-03',
  },
  {
    name: '赵六',
    age: 32,
    address: '深圳市南山区',
    status: 'pending',
    createTime: '2024-01-04',
  },
  {
    name: '钱七',
    age: 26,
    address: '杭州市西湖区',
    status: 'active',
    createTime: '2024-01-05',
  },
  {
    name: '孙八',
    age: 29,
    address: '南京市鼓楼区',
    status: 'inactive',
    createTime: '2024-01-06',
  },
  {
    name: '周九',
    age: 31,
    address: '武汉市洪山区',
    status: 'active',
    createTime: '2024-01-07',
  },
  {
    name: '吴十',
    age: 27,
    address: '成都市锦江区',
    status: 'pending',
    createTime: '2024-01-08',
  },
])

// ProTable列配置 - 包含筛选功能
const proTableColumns = ref<ProTableColumn[]>([
  {
    prop: 'FirstName',
    label: '姓名',
    width: 120,
    placeholder: '请输入姓名',
    fixed: 'left',
  },
  {
    prop: 'age',
    label: '年龄',
    width: 100,
    sortable: true,
    defaultValue: 10,
    align: 'center',
    valueType: 'digit',
  },
  {
    prop: 'status',
    label: '状态',
    width: 120,
    valueEnum: {
      active: {
        text: '激活',
      },
      inactive: {
        text: '未激活',
      },
      pending: {
        text: '待审核',
      },
    },
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 150,
    valueType: 'dateRange',
  },
  {
    prop: 'LastName',
    label: '地址',
    showOverflowTooltip: true,
    minWidth: 200,
  },
])
const request = async () => {
  // 方式1：使用原有的 executeApi 方法
  let api = {
    url: 'https://services.odata.org/V4/TripPinServiceRW/People',
    method: 'get' as const,
    protocol: 'odata' as const,
    odata: {
      select: 'FirstName,LastName,Gender',
      top: 5,
    },
    dataPath: 'value',
  }
  let ctx = { name: '张三' }
  const res = await executeApi(api, ctx)
  console.log('executeApi result:', res)
  return res
}

// 方式2：直接使用 OData 客户端（更强大的功能）
const requestWithODataClient = async () => {
  const client = createODataClient({
    baseUrl: 'https://services.odata.org/V4/TripPinServiceRW',
    timeout: 30000,
  })

  try {
    const result = await client.query('People', {
      select: 'FirstName,LastName,Gender',
      top: 5,
      count: true,
    })

    console.log('OData Client Result:', result)
    return result
  } catch (error: any) {
    console.error('OData Client Error:', error)
    return {
      data: [],
      total: 0,
      page: 1,
      pageSize: 5,
      hasNext: false,
      hasPrev: false,
      success: false,
      error: error.message,
    }
  }
}
// 计算当前页数据
const currentPageData = computed(() => {
  let filteredData = allProTableData.value

  // 应用筛选
  if (filters.value.name) {
    filteredData = filteredData.filter((item) =>
      item.name.includes(filters.value.name)
    )
  }
  if (filters.value.status) {
    filteredData = filteredData.filter(
      (item) => item.status === filters.value.status
    )
  }
  if (filters.value.createTime) {
    filteredData = filteredData.filter((item) =>
      item.createTime.includes(filters.value.createTime)
    )
  }

  // 更新总数
  pagination.value.total = filteredData.length

  // 分页
  const start = (pagination.value.current - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredData.slice(start, end)
})

// 事件处理方法
const handleFilterChange = (newFilters: ProTableFilters) => {
  filters.value = newFilters
  pagination.value.current = 1 // 重置到第一页
  console.log('筛选变化:', newFilters)
}

const handlePageChange = (current: number, pageSize: number) => {
  pagination.value.current = current
  pagination.value.pageSize = pageSize
  console.log('分页变化:', { current, pageSize })
}

const handleSortChange = (
  prop: string,
  order: 'ascending' | 'descending' | null
) => {
  console.log('排序变化:', { prop, order })
  // 这里可以实现排序逻辑
}

const handleRefresh = () => {
  loading.value = true
  console.log('刷新数据')
  // 模拟刷新
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const formLoading = ref(false)
const formData = ref({
  name: '',
  email: '',
  age: 18,
  gender: '',
  hobbies: [],
  birthday: '',
  isVip: false,
  city: '',
  description: '',
})

// const formConfig = ref<ProFormProps>({
//   size: 'small',
//   formItems: [
//     {
//       prop: 'name',
//       label: '姓名',
//       valueType: 'text',
//       required: true,
//       placeholder: '请输入姓名',
//       span: 12,
//       rules: [
//         { min: 2, max: 10, message: '姓名长度在2-10个字符', trigger: 'blur' },
//       ],
//     },
//     {
//       prop: 'email',
//       label: '邮箱',
//       valueType: 'text',
//       required: true,
//       placeholder: '请输入邮箱地址',
//       span: 12,
//       rules: [
//         { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
//       ],
//     },
//     {
//       prop: 'age',
//       label: '年龄',
//       valueType: 'text',
//       min: 1,
//       max: 120,
//       span: 12,
//     },
//     {
//       prop: 'gender',
//       label: '性别',
//       valueType: 'radio',
//       span: 12,
//       options: [
//         { label: '男', value: 'male' },
//         { label: '女', value: 'female' },
//       ],
//     },
//     {
//       prop: 'hobbies',
//       label: '爱好',
//       valueType: 'checkbox',
//       span: 24,
//       options: [
//         { label: '读书', value: 'reading' },
//         { label: '运动', value: 'sports' },
//         { label: '音乐', value: 'music' },
//         { label: '旅行', value: 'travel' },
//       ],
//     },
//     {
//       prop: 'birthday',
//       label: '生日',
//       valueType: 'date',
//       placeholder: '请选择生日',
//       span: 12,
//       format: 'YYYY-MM-DD',
//       valueFormat: 'YYYY-MM-DD',
//     },
//     {
//       prop: 'isVip',
//       label: 'VIP会员',
//       valueType: 'switch',
//       span: 12,
//     },
//     {
//       prop: 'city',
//       label: '城市',
//       valueType: 'select',
//       placeholder: '请选择城市',
//       span: 12,
//       options: [
//         { label: '北京', value: 'beijing' },
//         { label: '上海', value: 'shanghai' },
//         { label: '广州', value: 'guangzhou' },
//         { label: '深圳', value: 'shenzhen' },
//         { label: '杭州', value: 'hangzhou' },
//       ],
//     },
//     {
//       prop: 'description',
//       label: '个人描述',
//       valueType: 'textarea',
//       placeholder: '请输入个人描述',
//       span: 24,
//       maxlength: 200,
//       showWordLimit: true,
//     },
//   ],
// })

const handleFormSubmit = (data: Record<string, any>) => {
  formLoading.value = true
  console.log('表单提交:', data)

  // 模拟提交
  setTimeout(() => {
    formLoading.value = false
    alert('表单提交成功！')
  }, 1500)
}

const handleFormReset = () => {
  console.log('表单重置')
}

const handleFormChange = (prop: string, value: any) => {
  console.log('表单项变化:', { prop, value })
}

// RadioGroup相关数据
const radioValue1 = ref('option1')
const radioValue2 = ref('option2')
const radioValue3 = ref('')

// RadioGroup选项配置
const radioOptions = ref([
  { label: '选项一', value: 'option1' },
  { label: '选项二', value: 'option2' },
  { label: '选项三', value: 'option3' },
  { label: '禁用选项', value: 'option4', disabled: true },
])

// CheckboxGroup相关数据
const checkboxValue1 = ref(['option1'])
const checkboxValue2 = ref(['option1', 'option2'])
const checkboxValue3 = ref([])

// CheckboxGroup选项配置
const checkboxOptions = ref([
  { label: '选项一', value: 'option1' },
  { label: '选项二', value: 'option2' },
  { label: '选项三', value: 'option3' },
  { label: '禁用选项', value: 'option4', disabled: true },
])

// ConfigProvider相关数据
const isDark = ref(false)
const themeConfig = ref({
  primaryColor: '#ff6b6b',
  successColor: '#51cf66',
  warningColor: '#ffd43b',
  dangerColor: '#ff8787',
  borderRadius: '8px',
})

// MaterialRender 配置
const materialConfig = ref(pageData)

// OData 演示方法
const testExecuteApiGet = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 使用原有的 executeApi 方法
    const result = await executeApi({
      url: 'https://services.odata.org/V4/TripPinServiceRW/People',
      method: 'get' as const,
      protocol: 'odata' as const,
      odata: {
        select: 'FirstName,LastName,Gender',
        top: 5,
        filter: 'Gender eq "Female"',
      },
      dataPath: 'data',
    })

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '请求失败'
  } finally {
    loading.value = false
  }
}

const testODataClientQuery = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 使用新的 OData 客户端
    const client = createODataClient({
      baseUrl: 'https://services.odata.org/V4/TripPinServiceRW',
      timeout: 30000,
    })

    const result = await client.query('People', {
      select: 'FirstName,LastName,Gender',
      filter: 'Gender eq "Male"',
      orderby: 'FirstName asc',
      top: 5,
      count: true,
    })

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '请求失败'
  } finally {
    loading.value = false
  }
}

const testPagination = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    const client = createODataClient({
      baseUrl: 'https://services.odata.org/V4/TripPinServiceRW',
      timeout: 30000,
    })

    const result = await client.query('People', {
      select: 'FirstName,LastName,Gender,Age',
      top: 3,
      skip: 2,
      count: true,
      orderby: 'FirstName asc',
    })

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '请求失败'
  } finally {
    loading.value = false
  }
}

const testWithContext = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    const context = {
      gender: 'Female',
      minAge: 25,
      maxResults: 4,
    }

    const result = await executeApi(
      {
        url: 'https://services.odata.org/V4/TripPinServiceRW/People',
        method: 'get' as const,
        protocol: 'odata' as const,
        odata: {
          select: 'FirstName,LastName,Gender,Age',
          filter: 'Gender eq "{{gender}}"',
          top: 4, // 直接使用数字而不是模板变量
          orderby: 'FirstName desc',
        },
        dataPath: 'data',
      },
      context
    )

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '请求失败'
  } finally {
    loading.value = false
  }
}

const clearResults = () => {
  apiResult.value = null
  error.value = ''
}

// 缓存和去重功能演示方法
const testCacheFirst = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 使用缓存优先策略
    const result = await executeApi({
      url: 'https://services.odata.org/V4/TripPinServiceRW/People',
      method: 'get' as const,
      protocol: 'odata' as const,
      cache: {
        strategy: 'cache-first',
        ttl: 60000, // 1 分钟缓存
        enabled: true,
      },
      odata: {
        select: 'FirstName,LastName,Gender',
        top: 3,
        filter: 'Gender eq "Male"',
      },
    })

    apiResult.value = {
      ...result,
      message: '使用缓存优先策略 - 第一次请求会缓存，后续请求直接返回缓存',
    }
  } catch (err: any) {
    error.value = err.message || '缓存优先策略测试失败'
  } finally {
    loading.value = false
  }
}

const testNetworkFirst = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 使用网络优先策略
    const result = await executeApi({
      url: 'https://services.odata.org/V4/TripPinServiceRW/People',
      method: 'get' as const,
      protocol: 'odata' as const,
      cache: {
        strategy: 'network-first',
        ttl: 60000,
        enabled: true,
      },
      odata: {
        select: 'FirstName,LastName,Gender',
        top: 3,
        filter: 'Gender eq "Female"',
      },
    })

    apiResult.value = {
      ...result,
      message: '使用网络优先策略 - 优先网络请求，失败时使用缓存',
    }
  } catch (err: any) {
    error.value = err.message || '网络优先策略测试失败'
  } finally {
    loading.value = false
  }
}

const testRequestDedupe = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 同时发起多个相同的请求，测试去重功能
    const promises = Array.from({ length: 3 }, () =>
      executeApi({
        url: 'https://services.odata.org/V4/TripPinServiceRW/People',
        method: 'get' as const,
        protocol: 'odata' as const,
        odata: {
          select: 'FirstName,LastName',
          top: 2,
        },
      })
    )

    const results = await Promise.all(promises)

    apiResult.value = {
      data: results[0].data,
      success: true,
      total: results.length,
      message: `同时发起 ${promises.length} 个相同请求，去重机制确保只执行一次实际请求`,
    }
  } catch (err: any) {
    error.value = err.message || '请求去重测试失败'
  } finally {
    loading.value = false
  }
}

const testCacheStats = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 获取缓存统计信息
    const cacheManager = getGlobalCacheManager()
    const stats = cacheManager.getStats()

    apiResult.value = {
      data: stats,
      success: true,
      message: '当前缓存统计信息',
    }
  } catch (err: any) {
    error.value = err.message || '获取缓存统计失败'
  } finally {
    loading.value = false
  }
}

const clearCache = () => {
  try {
    const cacheManager = getGlobalCacheManager()
    cacheManager.clear()

    // 显示成功消息
    apiResult.value = {
      data: null,
      success: true,
      message: '缓存已清空',
    }
  } catch (err: any) {
    error.value = err.message || '清空缓存失败'
  }
}

// 高级功能演示方法
const testBatchRequests = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 导入批量请求功能
    const { executeBatchApi } = await import('@neue-plus/services')

    // 批量执行多个请求
    const configs = [
      {
        url: 'https://services.odata.org/V4/TripPinServiceRW/People',
        method: 'get' as const,
        protocol: 'odata' as const,
        odata: {
          select: 'FirstName,LastName',
          top: 2,
          filter: 'Gender eq "Male"',
        },
      },
      {
        url: 'https://services.odata.org/V4/TripPinServiceRW/People',
        method: 'get' as const,
        protocol: 'odata' as const,
        odata: {
          select: 'FirstName,LastName',
          top: 2,
          filter: 'Gender eq "Female"',
        },
      },
    ]

    const results = await executeBatchApi(configs)

    apiResult.value = {
      data: results,
      success: true,
      total: results.length,
      message: `批量执行了 ${results.length} 个请求`,
    }
  } catch (err: any) {
    error.value = err.message || '批量请求失败'
  } finally {
    loading.value = false
  }
}

const testConcurrentRequests = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 导入并发请求功能
    const { executeConcurrentApi } = await import('@neue-plus/services')

    // 并发执行多个请求（限制并发数为2）
    const configs = [
      {
        url: 'https://services.odata.org/V4/TripPinServiceRW/People',
        method: 'get' as const,
        protocol: 'odata' as const,
        odata: { select: 'FirstName', top: 1, skip: 0 },
      },
      {
        url: 'https://services.odata.org/V4/TripPinServiceRW/People',
        method: 'get' as const,
        protocol: 'odata' as const,
        odata: { select: 'FirstName', top: 1, skip: 1 },
      },
      {
        url: 'https://services.odata.org/V4/TripPinServiceRW/People',
        method: 'get' as const,
        protocol: 'odata' as const,
        odata: { select: 'FirstName', top: 1, skip: 2 },
      },
    ]

    const results = await executeConcurrentApi(configs, {}, 2) // 最多同时2个请求

    apiResult.value = {
      data: results,
      success: true,
      total: results.length,
      message: `并发执行了 ${results.length} 个请求（并发数限制为2）`,
    }
  } catch (err: any) {
    error.value = err.message || '并发请求失败'
  } finally {
    loading.value = false
  }
}

const testCustomService = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 导入服务相关功能
    const { createApiService, createODataClient } = await import(
      '@neue-plus/services'
    )

    // 创建自定义 API 服务
    const customService = createApiService()

    // 创建自定义 OData 客户端
    const customClient = createODataClient({
      baseUrl: 'https://services.odata.org/V4/TripPinServiceRW',
      timeout: 15000,
      headers: {
        'Custom-Header': 'neue-plus-demo',
      },
    })

    // 使用自定义客户端查询
    const result = await customClient.query('People', {
      select: 'FirstName,LastName,Gender',
      top: 3,
      orderby: 'FirstName asc',
    })

    // 获取缓存统计
    const { getCacheStats } = await import('@neue-plus/services')
    const cacheStats = getCacheStats()

    apiResult.value = {
      ...result,
      cacheStats,
      message: '使用自定义服务和客户端',
    }
  } catch (err: any) {
    error.value = err.message || '自定义服务测试失败'
  } finally {
    loading.value = false
  }
}

// CRUD 操作测试方法
const testCreateWithPost = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 注意：TripPin 服务是只读的，这个请求会失败，但可以演示功能
    const result = await executeApi({
      url: 'https://services.odata.org/V4/TripPinServiceRW/People',
      method: 'post' as const,
      protocol: 'odata' as const,
      body: {
        FirstName: 'John',
        LastName: 'Doe',
        Gender: 'Male',
        UserName: 'johndoe' + Date.now(),
      },
    })

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '创建失败（TripPin 服务是只读的）'
  } finally {
    loading.value = false
  }
}

const testUpdateWithPut = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 注意：TripPin 服务是只读的，这个请求会失败，但可以演示功能
    const result = await executeApi({
      url: 'https://services.odata.org/V4/TripPinServiceRW/People',
      method: 'put' as const,
      protocol: 'odata' as const,
      params: {
        id: 'russellwhyte',
      },
      body: {
        FirstName: 'Russell',
        LastName: 'Whyte',
        Gender: 'Male',
      },
    })

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '更新失败（TripPin 服务是只读的）'
  } finally {
    loading.value = false
  }
}

const testPatchEntity = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 注意：TripPin 服务是只读的，这个请求会失败，但可以演示功能
    const result = await executeApi({
      url: 'https://services.odata.org/V4/TripPinServiceRW/People',
      method: 'patch' as const,
      protocol: 'odata' as const,
      params: {
        id: 'russellwhyte',
      },
      body: {
        FirstName: 'Russell Updated',
      },
    })

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '部分更新失败（TripPin 服务是只读的）'
  } finally {
    loading.value = false
  }
}

const testDeleteEntity = async () => {
  loading.value = true
  error.value = ''
  apiResult.value = null

  try {
    // 注意：TripPin 服务是只读的，这个请求会失败，但可以演示功能
    const result = await executeApi({
      url: 'https://services.odata.org/V4/TripPinServiceRW/People',
      method: 'delete' as const,
      protocol: 'odata' as const,
      params: {
        id: 'russellwhyte',
      },
    })

    apiResult.value = result
  } catch (err: any) {
    error.value = err.message || '删除失败（TripPin 服务是只读的）'
  } finally {
    loading.value = false
  }
}
</script>
<style scoped>
.mt-4 {
  margin-top: 1rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.result-card {
  margin-top: 16px;
  background-color: #f8f9fa;
}

.result-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.result-data {
  max-height: 400px;
  overflow-y: auto;
}

.result-data pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.error {
  margin-top: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.el-button-group {
  flex-wrap: wrap;
  gap: 8px;
}

.el-button-group .el-button {
  margin: 0;
}

.demo-section {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.demo-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.demo-section .el-button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.demo-section .el-button {
  margin: 0;
  min-width: 120px;
}
</style>
