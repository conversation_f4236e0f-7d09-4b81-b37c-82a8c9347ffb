/**
 * 统一响应数据处理器
 * 用于处理 GET、POST、PUT、PATCH、DELETE 五种 HTTP 方法的响应数据
 * 自动判断返回数据是单条记录还是集合
 */

import { get } from 'lodash-unified'
import type { ApiConfig, ApiResponse } from './types'

/**
 * 数据类型枚举
 */
export enum DataType {
  SINGLE = 'single', // 单条记录
  COLLECTION = 'collection', // 集合数据
  EMPTY = 'empty', // 空数据
  UNKNOWN = 'unknown', // 未知类型
}

/**
 * 响应数据分析结果
 */
export interface ResponseAnalysis {
  dataType: DataType
  isArray: boolean
  itemCount: number
  hasMetadata: boolean
  hasPagination: boolean
}

/**
 * 处理配置选项
 */
export interface ProcessorOptions {
  // 强制指定数据类型
  forceDataType?: DataType
  // 自定义集合数据路径（如 OData 的 'value' 字段）
  collectionPath?: string
  // 自定义分页信息路径
  paginationPath?: string
  // 是否保留原始响应结构
  preserveOriginal?: boolean
  // 自定义数据提取逻辑
  customExtractor?: (data: any) => any
}

/**
 * 统一响应数据处理器类
 */
export class ResponseProcessor {
  /**
   * 分析响应数据类型
   */
  static analyzeResponse(data: any): ResponseAnalysis {
    if (data === null || data === undefined) {
      return {
        dataType: DataType.EMPTY,
        isArray: false,
        itemCount: 0,
        hasMetadata: false,
        hasPagination: false,
      }
    }

    // 检查是否为数组
    if (Array.isArray(data)) {
      return {
        dataType: data.length === 0 ? DataType.EMPTY : DataType.COLLECTION,
        isArray: true,
        itemCount: data.length,
        hasMetadata: false,
        hasPagination: false,
      }
    }

    // 检查是否为 OData 格式（包含 value 字段）
    if (data && typeof data === 'object' && 'value' in data) {
      const value = data.value
      return {
        dataType: Array.isArray(value)
          ? value.length === 0
            ? DataType.EMPTY
            : DataType.COLLECTION
          : DataType.SINGLE,
        isArray: Array.isArray(value),
        itemCount: Array.isArray(value) ? value.length : 1,
        hasMetadata: true,
        hasPagination: '@odata.count' in data || '@odata.nextLink' in data,
      }
    }

    // 检查是否为对象（单条记录）
    if (data && typeof data === 'object') {
      return {
        dataType: DataType.SINGLE,
        isArray: false,
        itemCount: 1,
        hasMetadata: false,
        hasPagination: false,
      }
    }

    return {
      dataType: DataType.UNKNOWN,
      isArray: false,
      itemCount: 0,
      hasMetadata: false,
      hasPagination: false,
    }
  }

  /**
   * 根据 HTTP 方法推断预期的数据类型
   */
  static inferDataTypeByMethod(method: string): DataType {
    const normalizedMethod = method.toLowerCase()

    switch (normalizedMethod) {
      case 'get':
        // GET 可能返回单条或集合，需要根据 URL 判断
        return DataType.UNKNOWN
      case 'post':
        // POST 通常返回创建的单条记录
        return DataType.SINGLE
      case 'put':
      case 'patch':
        // PUT/PATCH 通常返回更新后的单条记录
        return DataType.SINGLE
      case 'delete':
        // DELETE 通常返回空数据或确认信息
        return DataType.EMPTY
      default:
        return DataType.UNKNOWN
    }
  }

  /**
   * 根据 URL 推断数据类型
   */
  static inferDataTypeByUrl(url: string): DataType {
    // 移除查询参数
    const baseUrl = url.split('?')[0]

    // 检查是否包含具体的 ID 或 key（表示单条记录）
    const patterns = [
      /\/\d+$/, // 以数字结尾，如 /users/123
      /\/[^/]+\([^)]+\)$/, // OData key 格式，如 /People('key')
      /\/[a-f0-9-]{36}$/, // UUID 格式
      /\/[^/]+\/[^/]+$/, // 两级路径，如 /users/john
    ]

    for (const pattern of patterns) {
      if (pattern.test(baseUrl)) {
        return DataType.SINGLE
      }
    }

    // 默认认为是集合
    return DataType.COLLECTION
  }

  /**
   * 安全的路径数据提取工具 - 使用 lodash get 方法
   * 支持多种路径格式：
   * - 点分隔符: 'data.items'
   * - 数组索引: 'data.items[0]'
   * - 混合路径: 'response.data.items[0].name'
   */
  static extractDataByPath(obj: any, path: string, defaultValue?: any): any {
    if (!obj || !path) return defaultValue !== undefined ? defaultValue : obj
    return get(obj, path, defaultValue)
  }

  /**
   * 批量数据提取 - 支持多个路径尝试
   * 按顺序尝试每个路径，返回第一个成功的结果
   */
  static extractDataByPaths(
    obj: any,
    paths: string[],
    defaultValue?: any
  ): any {
    if (!obj || !paths || paths.length === 0)
      return defaultValue !== undefined ? defaultValue : obj

    for (const path of paths) {
      const result = get(obj, path)
      if (result !== undefined) {
        return result
      }
    }

    return defaultValue !== undefined ? defaultValue : obj
  }

  /**
   * 提取实际数据
   */
  static extractData(
    rawData: any,
    analysis: ResponseAnalysis,
    options: ProcessorOptions = {}
  ): any {
    if (options.customExtractor) {
      return options.customExtractor(rawData)
    }

    // 如果有自定义集合路径
    if (options.collectionPath && rawData && typeof rawData === 'object') {
      // 使用 lodash get 方法，如果路径不存在则返回原始数据
      const extractedData = this.extractDataByPath(
        rawData,
        options.collectionPath,
        rawData
      )
      return extractedData
    }

    // OData 格式处理
    if (analysis.hasMetadata && rawData && typeof rawData === 'object') {
      // 处理 OData 错误响应
      const errorMessage =
        get(rawData, 'error.message') ||
        get(rawData, 'error.innererror.message')
      if (errorMessage) {
        throw new Error(errorMessage)
      }

      // 处理 OData 集合响应 - 支持多种可能的数据路径
      const collectionData = this.extractDataByPaths(rawData, [
        'value', // 标准 OData 格式
        'd.results', // 旧版 OData 格式
        'data', // 自定义格式
        'items', // 通用集合格式
      ])

      if (Array.isArray(collectionData)) {
        return collectionData
      }

      // 处理 OData 单条记录响应（过滤掉元数据字段）
      if (typeof rawData === 'object') {
        const metadataKeys = [
          '@odata.context',
          '@odata.etag',
          '@odata.id',
          '@odata.type',
        ]
        const actualData = Object.keys(rawData)
          .filter((key) => !metadataKeys.includes(key))
          .reduce((obj, key) => {
            obj[key] = rawData[key]
            return obj
          }, {} as any)

        if (Object.keys(actualData).length > 0) {
          return actualData
        }
      }
    }

    // 直接返回数据
    return rawData
  }

  /**
   * 构建基础响应对象（不包含分页信息）
   */
  static buildBaseResponse<T = any>(
    data: T,
    success: boolean = true,
    error?: string
  ): ApiResponse<T> {
    const response: ApiResponse<T> = {
      data,
      success,
    }

    if (error) {
      response.error = error
    }

    return response
  }

  /**
   * 构建集合响应对象（包含分页信息）
   */
  static buildCollectionResponse<T = any>(
    data: T[],
    pagination: {
      total?: number
      page?: number
      pageSize?: number
      hasNext?: boolean
      hasPrev?: boolean
    } = {}
  ): ApiResponse<T[]> {
    return {
      data,
      success: true,
      total: pagination.total ?? data.length,
      page: pagination.page ?? 1,
      pageSize: pagination.pageSize ?? data.length,
      hasNext: pagination.hasNext ?? false,
      hasPrev: pagination.hasPrev ?? false,
    }
  }

  /**
   * 确定最终数据类型 - 优化后的逻辑
   */
  static determineFinalDataType(
    analysis: ResponseAnalysis,
    config: ApiConfig,
    options: ProcessorOptions
  ): DataType {
    // 1. 强制指定的数据类型优先级最高
    if (options.forceDataType) {
      return options.forceDataType
    }

    // 2. 如果响应数据分析结果明确，直接使用
    if (analysis.dataType !== DataType.UNKNOWN) {
      return analysis.dataType
    }

    // 3. 只有在数据分析结果不明确时，才使用方法和URL推断
    const expectedByMethod = this.inferDataTypeByMethod(config.method)
    if (expectedByMethod !== DataType.UNKNOWN) {
      return expectedByMethod
    }

    const expectedByUrl = this.inferDataTypeByUrl(config.url)
    if (expectedByUrl !== DataType.UNKNOWN) {
      return expectedByUrl
    }

    // 4. 默认返回集合类型
    return DataType.COLLECTION
  }

  /**
   * 提取分页信息
   */
  static extractPagination(
    rawData: any,
    analysis: ResponseAnalysis
  ): {
    total?: number
    page?: number
    pageSize?: number
    hasNext?: boolean
    hasPrev?: boolean
  } {
    const pagination: any = {}

    if (analysis.hasMetadata && rawData && typeof rawData === 'object') {
      // OData 分页信息 - 使用 lodash get 方法安全提取
      // 支持多种 OData 版本的分页格式
      const total = this.extractDataByPaths(rawData, [
        '@odata.count', // OData v4 标准格式
        'd.__count', // OData v2 格式
        'count', // 自定义格式
        'total', // 通用格式
        'totalCount', // 备选格式
      ])

      if (total !== undefined && total !== null) {
        pagination.total = Number(total)
      }

      // 检查是否有下一页
      const hasNext = this.extractDataByPaths(rawData, [
        '@odata.nextLink', // OData v4
        'd.__next', // OData v2
        'nextLink', // 自定义格式
        'hasNext', // 布尔值格式
      ])

      if (hasNext) {
        pagination.hasNext = typeof hasNext === 'boolean' ? hasNext : true
      }

      // 检查是否有上一页
      const hasPrev = this.extractDataByPaths(rawData, [
        '@odata.prevLink', // OData v4
        'd.__prev', // OData v2
        'prevLink', // 自定义格式
        'hasPrev', // 布尔值格式
      ])

      if (hasPrev) {
        pagination.hasPrev = typeof hasPrev === 'boolean' ? hasPrev : true
      }

      // 提取页面大小信息
      const pageSize = this.extractDataByPaths(rawData, [
        '@odata.top', // OData 查询参数
        'pageSize', // 自定义格式
        'limit', // 通用格式
        'size', // 备选格式
      ])

      if (pageSize !== undefined && pageSize !== null) {
        pagination.pageSize = Number(pageSize)
      }

      // 提取当前页信息
      const currentPage = this.extractDataByPaths(rawData, [
        '@odata.skip', // OData skip 参数（需要计算）
        'page', // 直接页码
        'currentPage', // 自定义格式
        'pageNumber', // 备选格式
      ])

      if (currentPage !== undefined && currentPage !== null) {
        if (typeof currentPage === 'number' && currentPage > 0) {
          pagination.page = currentPage
        } else if (typeof currentPage === 'number' && pagination.pageSize) {
          // 如果是 skip 值，计算页码
          pagination.page = Math.floor(currentPage / pagination.pageSize) + 1
        }
      }
    }

    // 如果是数组，设置基本信息
    if (analysis.isArray) {
      if (!pagination.pageSize) {
        pagination.pageSize = analysis.itemCount
      }
      if (!pagination.total) {
        pagination.total = analysis.itemCount
      }
      if (!pagination.page) {
        pagination.page = 1
      }
    }

    return pagination
  }

  /**
   * 统一处理响应数据
   */
  static processResponse<T = any>(
    rawData: any,
    config: ApiConfig,
    options: ProcessorOptions = {}
  ): ApiResponse<T> {
    console.log(rawData, config, options)
    try {
      // 分析响应数据
      const analysis = this.analyzeResponse(rawData)

      // 确定最终数据类型 - 优化后的逻辑
      const finalDataType = this.determineFinalDataType(
        rawData,
        analysis,
        config,
        options
      )

      // 提取实际数据
      const extractedData = this.extractData(rawData, analysis, options)

      // 根据数据类型构建不同的响应格式
      let response: ApiResponse<T>
      console.log(finalDataType, extractedData, 'finalDataType')
      if (finalDataType === DataType.COLLECTION || analysis.isArray) {
        // 集合数据 - 包含分页信息
        const pagination = this.extractPagination(rawData, analysis)
        response = this.buildCollectionResponse(
          extractedData,
          pagination
        ) as ApiResponse<T>
      } else if (finalDataType === DataType.EMPTY) {
        // 空数据 - 最简响应
        response = this.buildBaseResponse(extractedData, true)
      } else {
        // 单条记录 - 基础响应（不包含分页信息）
        response = this.buildBaseResponse(extractedData, true)
      }

      // 添加数据类型信息（调试用）
      if (process.env.NODE_ENV === 'development') {
        ;(response as any)._debug = {
          analysis,
          finalDataType,
          method: config.method,
          url: config.url,
          originalData: options.preserveOriginal ? rawData : undefined,
        }
      }
      console.log(response)
      return response
    } catch (error) {
      // 错误处理
      return this.buildBaseResponse(
        null as any,
        false,
        error instanceof Error ? error.message : 'Response processing failed'
      )
    }
  }

  /**
   * 便捷方法：处理 GET 请求响应
   */
  static processGetResponse<T = any>(
    rawData: any,
    config: ApiConfig,
    options: ProcessorOptions = {}
  ): ApiResponse<T> {
    console.log(config, 'config')
    return this.processResponse(rawData, config, {
      ...options,
      forceDataType:
        options.forceDataType || this.inferDataTypeByUrl(config.url),
    })
  }

  /**
   * 便捷方法：处理 POST 请求响应
   */
  static processPostResponse<T = any>(
    rawData: any,
    config: ApiConfig,
    options: ProcessorOptions = {}
  ): ApiResponse<T> {
    return this.processResponse(rawData, config, {
      ...options,
      forceDataType: options.forceDataType || DataType.SINGLE,
    })
  }

  /**
   * 便捷方法：处理 PUT/PATCH 请求响应
   */
  static processUpdateResponse<T = any>(
    rawData: any,
    config: ApiConfig,
    options: ProcessorOptions = {}
  ): ApiResponse<T> {
    return this.processResponse(rawData, config, {
      ...options,
      forceDataType: options.forceDataType || DataType.SINGLE,
    })
  }

  /**
   * 便捷方法：处理 DELETE 请求响应
   */
  static processDeleteResponse<T = any>(
    rawData: any,
    config: ApiConfig,
    options: ProcessorOptions = {}
  ): ApiResponse<T> {
    return this.processResponse(rawData, config, {
      ...options,
      forceDataType: options.forceDataType || DataType.EMPTY,
    })
  }
}
