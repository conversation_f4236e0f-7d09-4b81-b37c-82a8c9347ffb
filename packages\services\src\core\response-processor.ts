/**
 * 统一响应数据处理器
 * 用于处理 GET、POST、PUT、PATCH、DELETE 五种 HTTP 方法的响应数据
 * 自动判断返回数据是单条记录还是集合
 */

import type { ApiConfig, ApiResponse } from './types'

/**
 * 数据类型枚举
 */
export enum DataType {
  SINGLE = 'single', // 单条记录
  COLLECTION = 'collection', // 集合数据
  EMPTY = 'empty', // 空数据
  UNKNOWN = 'unknown', // 未知类型
}

/**
 * 响应数据分析结果
 */
export interface ResponseAnalysis {
  dataType: DataType
  isArray: boolean
  itemCount: number
  hasMetadata: boolean
  hasPagination: boolean
}

/**
 * 处理配置选项
 */
export interface ProcessorOptions {
  // 强制指定数据类型
  forceDataType?: DataType
  // 自定义集合数据路径（如 OData 的 'value' 字段）
  collectionPath?: string
  // 自定义分页信息路径
  paginationPath?: string
  // 是否保留原始响应结构
  preserveOriginal?: boolean
  // 自定义数据提取逻辑
  customExtractor?: (data: any) => any
}

/**
 * 统一响应数据处理器类
 */
export class ResponseProcessor {
  /**
   * 分析响应数据类型
   */
  static analyzeResponse(data: any): ResponseAnalysis {
    if (data === null || data === undefined) {
      return {
        dataType: DataType.EMPTY,
        isArray: false,
        itemCount: 0,
        hasMetadata: false,
        hasPagination: false,
      }
    }

    // 检查是否为数组
    if (Array.isArray(data)) {
      return {
        dataType: data.length === 0 ? DataType.EMPTY : DataType.COLLECTION,
        isArray: true,
        itemCount: data.length,
        hasMetadata: false,
        hasPagination: false,
      }
    }

    // 检查是否为 OData 格式（包含 value 字段）
    if (data && typeof data === 'object' && 'value' in data) {
      const value = data.value
      return {
        dataType: Array.isArray(value)
          ? value.length === 0
            ? DataType.EMPTY
            : DataType.COLLECTION
          : DataType.SINGLE,
        isArray: Array.isArray(value),
        itemCount: Array.isArray(value) ? value.length : 1,
        hasMetadata: true,
        hasPagination: '@odata.count' in data || '@odata.nextLink' in data,
      }
    }

    // 检查是否为对象（单条记录）
    if (data && typeof data === 'object') {
      return {
        dataType: DataType.SINGLE,
        isArray: false,
        itemCount: 1,
        hasMetadata: false,
        hasPagination: false,
      }
    }

    return {
      dataType: DataType.UNKNOWN,
      isArray: false,
      itemCount: 0,
      hasMetadata: false,
      hasPagination: false,
    }
  }

  /**
   * 根据 HTTP 方法推断预期的数据类型
   */
  static inferDataTypeByMethod(method: string): DataType {
    const normalizedMethod = method.toLowerCase()

    switch (normalizedMethod) {
      case 'get':
        // GET 可能返回单条或集合，需要根据 URL 判断
        return DataType.UNKNOWN
      case 'post':
        // POST 通常返回创建的单条记录
        return DataType.SINGLE
      case 'put':
      case 'patch':
        // PUT/PATCH 通常返回更新后的单条记录
        return DataType.SINGLE
      case 'delete':
        // DELETE 通常返回空数据或确认信息
        return DataType.EMPTY
      default:
        return DataType.UNKNOWN
    }
  }

  /**
   * 根据 URL 推断数据类型
   */
  static inferDataTypeByUrl(url: string): DataType {
    // 移除查询参数
    const baseUrl = url.split('?')[0]

    // 检查是否包含具体的 ID 或 key（表示单条记录）
    const patterns = [
      /\/\d+$/, // 以数字结尾，如 /users/123
      /\/[^\/]+\([^)]+\)$/, // OData key 格式，如 /People('key')
      /\/[a-f0-9-]{36}$/, // UUID 格式
      /\/[^\/]+\/[^\/]+$/, // 两级路径，如 /users/john
    ]

    for (const pattern of patterns) {
      if (pattern.test(baseUrl)) {
        return DataType.SINGLE
      }
    }

    // 默认认为是集合
    return DataType.COLLECTION
  }

  /**
   * 提取实际数据
   */
  static extractData(
    rawData: any,
    analysis: ResponseAnalysis,
    options: ProcessorOptions = {}
  ): any {
    if (options.customExtractor) {
      return options.customExtractor(rawData)
    }

    // 如果有自定义集合路径
    if (options.collectionPath && rawData && typeof rawData === 'object') {
      const pathParts = options.collectionPath.split('.')
      let data = rawData
      for (const part of pathParts) {
        data = data?.[part]
      }
      return data
    }

    // OData 格式处理
    if (analysis.hasMetadata && rawData && 'value' in rawData) {
      return rawData.value
    }

    // 直接返回数据
    return rawData
  }

  /**
   * 构建基础响应对象（不包含分页信息）
   */
  static buildBaseResponse<T = any>(
    data: T,
    success: boolean = true,
    error?: string
  ): ApiResponse<T> {
    const response: ApiResponse<T> = {
      data,
      success,
    }

    if (error) {
      response.error = error
    }

    return response
  }

  /**
   * 构建集合响应对象（包含分页信息）
   */
  static buildCollectionResponse<T = any>(
    data: T[],
    pagination: {
      total?: number
      page?: number
      pageSize?: number
      hasNext?: boolean
      hasPrev?: boolean
    } = {}
  ): ApiResponse<T[]> {
    return {
      data,
      success: true,
      total: pagination.total ?? data.length,
      page: pagination.page ?? 1,
      pageSize: pagination.pageSize ?? data.length,
      hasNext: pagination.hasNext ?? false,
      hasPrev: pagination.hasPrev ?? false,
    }
  }

  /**
   * 提取分页信息
   */
  static extractPagination(
    rawData: any,
    analysis: ResponseAnalysis
  ): {
    total?: number
    page?: number
    pageSize?: number
    hasNext?: boolean
    hasPrev?: boolean
  } {
    const pagination: any = {}

    if (analysis.hasMetadata && rawData && typeof rawData === 'object') {
      // OData 分页信息
      if ('@odata.count' in rawData) {
        pagination.total = rawData['@odata.count']
      }
      if ('@odata.nextLink' in rawData) {
        pagination.hasNext = true
      }
      if ('@odata.prevLink' in rawData) {
        pagination.hasPrev = true
      }
    }

    // 如果是数组，设置基本信息
    if (analysis.isArray) {
      pagination.pageSize = analysis.itemCount
      if (!pagination.total) {
        pagination.total = analysis.itemCount
      }
    }

    return pagination
  }

  /**
   * 统一处理响应数据
   */
  static processResponse<T = any>(
    rawData: any,
    config: ApiConfig,
    options: ProcessorOptions = {}
  ): ApiResponse<T> {
    try {
      // 分析响应数据
      const analysis = this.analyzeResponse(rawData)

      // 推断预期数据类型
      const expectedByMethod = this.inferDataTypeByMethod(config.method)
      const expectedByUrl = this.inferDataTypeByUrl(config.url)

      // 确定最终数据类型
      let finalDataType = analysis.dataType
      if (options.forceDataType) {
        finalDataType = options.forceDataType
      } else if (analysis.dataType === DataType.UNKNOWN) {
        finalDataType =
          expectedByMethod !== DataType.UNKNOWN
            ? expectedByMethod
            : expectedByUrl
      }

      // 提取实际数据
      const extractedData = this.extractData(rawData, analysis, options)

      // 根据数据类型构建不同的响应格式
      let response: ApiResponse<T>

      if (finalDataType === DataType.COLLECTION || analysis.isArray) {
        // 集合数据 - 包含分页信息
        const pagination = this.extractPagination(rawData, analysis)
        response = this.buildCollectionResponse(
          extractedData,
          pagination
        ) as ApiResponse<T>
      } else if (finalDataType === DataType.EMPTY) {
        // 空数据 - 最简响应
        response = this.buildBaseResponse(extractedData, true)
      } else {
        // 单条记录 - 基础响应（不包含分页信息）
        response = this.buildBaseResponse(extractedData, true)
      }

      // 添加数据类型信息（调试用）
      if (process.env.NODE_ENV === 'development') {
        ;(response as any)._debug = {
          analysis,
          expectedByMethod,
          expectedByUrl,
          finalDataType,
          originalData: options.preserveOriginal ? rawData : undefined,
        }
      }

      return response
    } catch (error) {
      // 错误处理
      return this.buildBaseResponse(
        null as any,
        false,
        error instanceof Error ? error.message : 'Response processing failed'
      )
    }
  }

  /**
   * 便捷方法：处理 GET 请求响应
   */
  static processGetResponse<T = any>(
    rawData: any,
    config: ApiConfig,
    options: ProcessorOptions = {}
  ): ApiResponse<T> {
    return this.processResponse(rawData, config, {
      ...options,
      forceDataType:
        options.forceDataType || this.inferDataTypeByUrl(config.url),
    })
  }

  /**
   * 便捷方法：处理 POST 请求响应
   */
  static processPostResponse<T = any>(
    rawData: any,
    config: ApiConfig,
    options: ProcessorOptions = {}
  ): ApiResponse<T> {
    return this.processResponse(rawData, config, {
      ...options,
      forceDataType: options.forceDataType || DataType.SINGLE,
    })
  }

  /**
   * 便捷方法：处理 PUT/PATCH 请求响应
   */
  static processUpdateResponse<T = any>(
    rawData: any,
    config: ApiConfig,
    options: ProcessorOptions = {}
  ): ApiResponse<T> {
    return this.processResponse(rawData, config, {
      ...options,
      forceDataType: options.forceDataType || DataType.SINGLE,
    })
  }

  /**
   * 便捷方法：处理 DELETE 请求响应
   */
  static processDeleteResponse<T = any>(
    rawData: any,
    config: ApiConfig,
    options: ProcessorOptions = {}
  ): ApiResponse<T> {
    return this.processResponse(rawData, config, {
      ...options,
      forceDataType: options.forceDataType || DataType.EMPTY,
    })
  }
}
